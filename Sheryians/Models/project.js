const mongoose = require("mongoose");

const projectSchema = new mongoose.Schema(
    {
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user",
            required: true,
        },
        githubLink: {
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return v.includes("github.com");
                },
                message: "Please provide a valid GitHub repository URL",
            },
        },
        liveLink: {
            type: String,
            default: null,
            validate: {
                validator: function (v) {
                    if (!v) return true; // Allow null/empty values
                    try {
                        new URL(v);
                        return true;
                    } catch (err) {
                        return false;
                    }
                },
                message: "Please provide a valid URL for live demo",
            },
        },
        status: {
            type: String,
            enum: ["pending", "approved", "rejected"],
            default: "pending",
        },
        submittedAt: {
            type: Date,
            default: Date.now,
        },
        reviewedAt: {
            type: Date,
            default: null,
        },
        reviewedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user",
            default: null,
        },
        reviewNotes: {
            type: String,
            default: null,
        },
    },
    {
        timestamps: true,
    }
);

// Create indexes for better performance
projectSchema.index({ user: 1, submittedAt: -1 });
projectSchema.index({ status: 1 });

// Virtual for project title (extracted from GitHub URL)
projectSchema.virtual("projectTitle").get(function () {
    if (this.githubLink) {
        const parts = this.githubLink.split("/");
        return parts[parts.length - 1] || "Unknown Project";
    }
    return "Unknown Project";
});

// Method to check if project belongs to user
projectSchema.methods.belongsToUser = function (userId) {
    return this.user.toString() === userId.toString();
};

// Static method to get user's projects
projectSchema.statics.getUserProjects = function (userId) {
    return this.find({ user: userId }).sort({ submittedAt: -1 });
};

// Static method to get pending projects for admin
projectSchema.statics.getPendingProjects = function () {
    return this.find({ status: "pending" }).populate("user", "name email").sort({ submittedAt: -1 });
};

module.exports = mongoose.model("Project", projectSchema);
