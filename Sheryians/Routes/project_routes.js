const express = require("express");
const router = express.Router();
const projectController = require("../Controllers/project_controller");
const authMiddleware = require("../Config/auth.middleware");
const { redirectTo } = require("../utils/utils.js");

// Admin middleware (you'll need to customize this based on your user model)
const adminMiddleware = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: "Authentication required",
        });
    }

    // Check if user is admin (customize this condition based on your user model)
    if (req.user.admin) {
        return next();
    }

    return res.status(403).json({
        success: false,
        message: "Admin privileges required",
    });
};

// Protected routes - require authentication
router.use(authMiddleware.isAuthenticated);

// GET /projects/upload - Show project upload form
router.get("/", redirectTo, projectController.getProjectUploadPage);

// GET /projects/my-projects-page - Show user projects page
router.get("/my-projects", redirectTo, projectController.getUserProjectsPage);

// POST /projects/submit - Submit a new project
router.post("/submit", projectController.submitProject);

// GET /projects/user/my-projects - Get user's submitted projects (API)
router.get("/user/my-projects", projectController.getUserProjects);

// Admin routes - require admin privileges
router.use("/admin", adminMiddleware);

// GET /projects/admin - Show admin projects page
router.get("/admin", projectController.getAdminProjectsPage);

// GET /projects/admin/all - Get all projects (admin only)
router.get("/admin/all", projectController.getAllProjects);

// GET /projects/admin/pending - Get all pending projects (admin only)
router.get("/admin/pending", projectController.getPendingProjects);

// PUT /projects/admin/:projectId/status - Update project status (admin only)
router.put("/admin/:projectId/status", projectController.updateProjectStatus);

module.exports = router;
