const Project = require("../Models/project");

// Render project upload page
module.exports.getProjectUploadPage = (req, res) => {
    try {
        return res.render("projectUpload.ejs", {
            title: "Submit Your Project",
            user: req.user,
        });
    } catch (error) {
        console.error("Error rendering project upload page:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// Submit a new project
module.exports.submitProject = async (req, res) => {
    try {
        const { githubLink, liveLink } = req.body;
        const userId = req.user._id;

        // Validate required fields
        if (!githubLink) {
            return res.status(400).json({
                success: false,
                message: "GitHub repository link is required",
            });
        }

        // Validate GitHub URL
        if (!githubLink.includes("github.com")) {
            return res.status(400).json({
                success: false,
                message: "Please provide a valid GitHub repository URL",
            });
        }

        // Check for duplicate submissions
        const existingProject = await Project.findOne({
            user: userId,
            githubLink: githubLink,
        });

        if (existingProject) {
            return res.status(400).json({
                success: false,
                message: "You have already submitted this project",
            });
        }

        // Create new project submission
        const newProject = new Project({
            user: userId,
            githubLink: githubLink.trim(),
            liveLink: liveLink ? liveLink.trim() : null,
        });

        await newProject.save();

        return res.status(201).json({
            success: true,
            message: "Project submitted successfully! We will review it soon.",
            project: {
                id: newProject._id,
                githubLink: newProject.githubLink,
                liveLink: newProject.liveLink,
                status: newProject.status,
                submittedAt: newProject.submittedAt,
            },
        });
    } catch (error) {
        console.error("Error submitting project:", error);

        // Handle validation errors
        if (error.name === "ValidationError") {
            const errors = Object.values(error.errors).map((err) => err.message);
            return res.status(400).json({
                success: false,
                message: errors.join(". "),
            });
        }

        return res.status(500).json({
            success: false,
            message: "Internal server error. Please try again later.",
        });
    }
};

// Render user projects page
module.exports.getUserProjectsPage = (req, res) => {
    try {
        return res.render("userProjects.ejs", {
            title: "My Projects",
            user: req.user,
        });
    } catch (error) {
        console.error("Error rendering user projects page:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// Get user's submitted projects (API endpoint)
module.exports.getUserProjects = async (req, res) => {
    try {
        const userId = req.user._id;
        const projects = await Project.getUserProjects(userId);
        
        const projectsWithTitle = projects.map((project) => ({
            ...project.toObject(),
            projectTitle: project.projectTitle,
        }));

        return res.status(200).json({
            success: true,
            projects: projectsWithTitle,
        });
    } catch (error) {
        console.error("Error fetching user projects:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// Admin: Render admin projects page
module.exports.getAdminProjectsPage = (req, res) => {
    try {
        // Check if user is admin
        if (!req.user.admin) {
            return res.status(403).render("error", {
                title: "Access Denied",
                message: "Admin privileges required to access this page.",
            });
        }

        return res.render("adminProjects.ejs", {
            title: "Admin - Project Management",
            user: req.user,
        });
    } catch (error) {
        console.error("Error rendering admin projects page:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// Admin: Get all projects (for admin page)
module.exports.getAllProjects = async (req, res) => {
    try {
        // Check if user is admin
        if (!req.user.admin) {
            return res.status(403).json({
                success: false,
                message: "Access denied. Admin privileges required.",
            });
        }

        const projects = await Project.find({}).populate("user", "name email").populate("reviewedBy", "name").sort({ submittedAt: -1 });

        // Add project title from GitHub URL
        const projectsWithTitle = projects.map((project) => ({
            ...project.toObject(),
            projectTitle: project.projectTitle,
        }));

        return res.status(200).json({
            success: true,
            projects: projectsWithTitle,
        });
    } catch (error) {
        console.error("Error fetching all projects:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// Admin: Get all pending projects
module.exports.getPendingProjects = async (req, res) => {
    try {
        // Check if user is admin (you'll need to implement admin check)
        if (!req.user.isAdmin) {
            return res.status(403).json({
                success: false,
                message: "Access denied. Admin privileges required.",
            });
        }

        const projects = await Project.getPendingProjects();

        return res.status(200).json({
            success: true,
            projects: projects,
        });
    } catch (error) {
        console.error("Error fetching pending projects:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

// Admin: Update project status
module.exports.updateProjectStatus = async (req, res) => {
    try {
        // Check if user is admin
        if (!req.user.admin) {
            return res.status(403).json({
                success: false,
                message: "Access denied. Admin privileges required.",
            });
        }

        const { projectId } = req.params;
        const { status, reviewNotes } = req.body;

        // Validate status
        if (!["pending", "approved", "rejected"].includes(status)) {
            return res.status(400).json({
                success: false,
                message: "Invalid status. Must be pending, approved, or rejected.",
            });
        }

        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({
                success: false,
                message: "Project not found",
            });
        }

        // Update project
        project.status = status;
        project.reviewedAt = new Date();
        project.reviewedBy = req.user._id;
        if (reviewNotes) {
            project.reviewNotes = reviewNotes;
        }

        await project.save();

        return res.status(200).json({
            success: true,
            message: `Project ${status} successfully`,
            project: project,
        });
    } catch (error) {
        console.error("Error updating project status:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};
