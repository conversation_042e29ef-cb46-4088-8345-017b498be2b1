const ProfileDp = require('../Models/profile_dp.js');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');
const Imagekit = require('imagekit');
const Course = require('../Models/Courses.js');
const Users = require('../Models/user.js');
const ZeroToOne = require('../Models/zero_to_one.js');
const homePageModel = require('../Models/homePageModel.js')
const Default_dp = require('../Models/default_dp.js');
const NodeCache = require("node-cache");
const homeCache = new NodeCache({ stdTTL: 3600 });
const axios = require('axios');
const Redis = require('ioredis');
const user = require('../Models/user.js');

const redis = new Redis(process.env.REDIS_URL_DISCORD);

const imagekit = new Imagekit({
    publicKey: 'public_DQRGKIIs98Gr4cHv0e2XAn73I2U=',
    privateKey: 'private_Hk3XPFhDJiyZSBfqdx+bhm537O4=',
    urlEndpoint: 'https://ik.imagekit.io/sheryians'
});

const API_KEY = process.env.YOUTUBE_API; // Replace with your API key
const CHANNEL_ID = process.env.CHANNEL_ID; // Replace with the channel ID you want to query

async function getYoutubeData(){
    console.log("getting data from youtube")
    const response = await axios.get(`https://www.googleapis.com/youtube/v3/channels`, {
        params: {
            part: 'statistics',
            id: CHANNEL_ID,
            key: API_KEY,
        },
    });
    return response.data.items[0].statistics.subscriberCount
}

async function getAllImageURLs(folderPath) {
    try {
        const fileList = await imagekit.listFiles({ path: "defaulticon" });
        const imageUrls = fileList.map((file) => file.url);
        imageUrls.forEach(async (url) => {
            link = url
            naae = url.split("/")[ 5 ].split("_")[ 0 ] + "_" + url.split("/")[ 5 ].split("_")[ 1 ]
            await Default_dp.create({
                name: naae,
                url: link
            });
            console.log("done");
        });
    } catch (error) {
        console.error('Error fetching image URLs:', error);
        return [];
    }
}
//   getAllImageURLs("defaulticon");

module.exports.getHome = async (req, res) => {
    try {
        // await getYoutubeData()
        
        const homePageCacheKey = 'homepage_data';

        // Check if data is cached 
        // const cachedData = homeCache.get(homePageCacheKey);
        const cachedData = false;

        if (cachedData) {
            return renderHomePage(JSON.parse(cachedData));
        } else {
            // If not cached, query the database
            const courses = await Course.find({
                course_status: 'active',
            }).sort({ createdAt: -1 });
            const homePage = {
                courses: courses,
            };

            // Store the data in the cache for future use
            homeCache.set(homePageCacheKey, JSON.stringify(homePage)); // Cache for 1 hour (in seconds)

            return renderHomePage(homePage);
        }
    } catch (err) {
        console.error(err);
        return res.status(500).send('Internal Server Error');
    }

    async function renderHomePage(homePage) {


        if (!req.user) {
            if (req.originalUrl) {
                req.session.returnTo = req.originalUrl;
            }
        }
        let cachedSubs = await redis.get("subscribers");

        if(!cachedSubs){
            const subs = await getYoutubeData()
            await redis.set("subscribers", subs, 'EX', 24 * 60 * 60);
            cachedSubs = subs;
        }
        return res.render('index', {
            subs:cachedSubs >= 1e6 ? (cachedSubs / 1e6).toFixed(1).replace(/\.0$/, '') + 'M' : cachedSubs >= 1e3 ? (cachedSubs / 1e3).toFixed(1).replace(/\.0$/, '') + 'K' : cachedSubs.toString(),
            pageTitle: "Home",
            title: 'Sheryians Coding School | Learn Coding in India',
            request: req.user ? req : null,
            user: req.user ? req.user : null,
            homePage,
        });
    }
};



module.exports.offline = async (req, res) => {
    return res.sendFile(path.join(__dirname, "../Views/offline.html"));

}
module.exports.UploadProfile_dp = async (req, res) => {
    let profile_dp = await ProfileDp.findOne({ user_id: req.user.id });
    if (!profile_dp) {
        profile_dp = new ProfileDp();
    }
    try {
        ProfileDp.uploaded_dp(req, res, async (err) => {
            if (err) {
                console.log(err);
                return;
            }
            const { filename: image } = req.file;
            sharp.cache(false);
            await sharp(req.file.path)
                .resize(200, 200)
                .withMetadata()
                .jpeg({ quality: 90 })
                .toFile(
                    path.resolve(req.file.destination + '\\resized\\' + image)
                )
            fs.unlinkSync(req.file.path)
            if (profile_dp.dp) {
                if (profile_dp.dp.search('google') == -1) {
                    //  console.log("hellllooo",path.join(__dirname, '..',profile_dp.dp))
                    // fs.unlinkSync(path.join(__dirname, '..',profile_dp.dp))
                }
            }
            fs.readFile(path.resolve(req.file.destination + '\\resized\\' + image), (err, data) => {
                if (err) {
                    console.log(err);
                    return;
                }
                imagekit.upload({
                    file: data,
                    fileName: image,
                    folder: 'profile_dp'
                }).then(async (response) => {
                    console.log(response, "lol");
                    profile_dp.dp = response.url;
                    profile_dp.user_id = req.user.id;
                    await profile_dp.save();
                    return res.redirect('back');
                });
            }
            )
        }
        )
    } catch (err) {
        console.log(err);
        return;
    }
}

// module.exports.completePaymnet = async (req, res)=>{
//     if(req.user.admin){
//         const course = await Course.findOne({_id:req.params.course_id})
//         course.enrolled_students.filter(student=>student.student_id == req.params.id)[0].status = "paid";
//         course.save();
//         res.send("done")
//     }else{
//         res.send("not authorized")
//     }
// }

module.exports.getInertia = (req, res) => {
    res.sendFile(path.join(__dirname, '../Views/error_page.html'));
    return
    return res.render('inertia.ejs', {
        title: 'Inertia By Sheryians | Sheryians Coding School',
        request: req
    });
}

module.exports.getZeroToOne = (req, res) => {
    return res.render('zero_to_one.ejs', {
        title: 'Zero To One | Sheryians Coding School',
        request: req
    });
}


module.exports.defaultdp = async (req, res) => {

}

module.exports.comingSoon = async (req, res) => {
    res.render("comingSoonPage", {
        title: "Coming Soon",
        pageTitle: "Coming Soon",
        request: req.user ? req : null
    })
}

module.exports.hireFromUsStatus = async (req, res) => {
    res.render("hireFromUsStatus", {
        title: "Hire From Us - Status",
        pageTitle: "Hire From Us",
        request: req.user ? req : null
    })
}

module.exports.aboutUs = async (req, res) => {
    res.render("aboutUs", {
        title: "About Us",
        pageTitle: "About Us",
        request: req.user ? req : null
    })
}

module.exports.addEnrolledStudents = async (req, res) => {
    const users = await Users.find({});
    users.forEach(async (user) => {
        user.feeStatus.forEach(async (fee) => {
            const crse = await Course.findOne({ _id: fee.course_id }).select('+enrolled_students');
            if (crse) {
                console.log(crse.course_name);
                crse.enrolled_students.push({
                    student_id: user._id,
                    status: fee.status
                });
                crse.displaytag = "INDEPTH"
                await crse.save();
            }
        })
        await user.save();
    })
}
module.exports.addorderid = async (req, res) => {
    console.log("started");
    const crse = await Course.find({}).sort({ createdAt: -1 });
    var lrng = crse.length
    crse.forEach(async (course, i) => {
        course.order = Number(i + 1)
        console.log(course.course_name, course.order);
        await course.save((e, err) => {
            if (e) {
                console.log("this course:", course.course_name);
            }
        });
    })
    res.send("done");
}

module.exports.registerZeroToOne = async (req, res) => {
    try {
        let zeroToOne = await ZeroToOne.findOne({
            email: req.body.email
        });
        if (zeroToOne) {
            req.flash('error', 'Email already registered');
            return res.redirect('back');
        }
        zeroToOne = new ZeroToOne();
        zeroToOne.name = req.body.name;
        zeroToOne.email = req.body.email;
        zeroToOne.phoneNumber = req.body.phoneNumber;
        zeroToOne.college = req.body.college;
        zeroToOne.degree = req.body.degree;
        zeroToOne.semester = req.body.semester;
        zeroToOne.branch = req.body.branch;
        zeroToOne.drink = req.body.drink;
        await zeroToOne.save();
        req.flash('success', 'Registered Successfully');
        return res.redirect('back');
    } catch (err) {
        console.log(err);
        return;
    }
}