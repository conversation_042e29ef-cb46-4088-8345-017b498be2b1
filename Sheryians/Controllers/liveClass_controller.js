const axios = require('axios');
const jwt = require('jsonwebtoken');
const LiveClass = require('../Models/liveClass');
const { c } = require('compile-run');
const User = require('../Models/user.js');
const Courses = require('../Models/Courses.js');
const json = require('body-parser/lib/types/json.js');

// chat token generation
const generateChatToken = (payload, chatSecret) => {
  // use hs256
  if(!payload.moderator){
    payload = JSON.stringify(payload);
  }
  return jwt.sign(payload, chatSecret, { algorithm: 'HS256' });
}

module.exports.createLiveStream = async (req, res) => {
  if(req.user.admin === false) {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  try {
    const response = await axios.post('https://www.vdocipher.com/api/livestream', {
      title: req.body.title,
      chatType: 'authenticated'
      // allowedDomains: process.env.NODE_ENV === 'production' ? "sheryians.com,www.sheryians.com" : "sheryians.com,www.sheryians.com",
    //   tagCsv: req.body.tagCsv
    }, {
      headers: {
        'Authorization': `Apisecret ${process.env.VDO_API_SECRET}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(response)
    res.json(response.data);
  } catch (error) {
    console.log(error)
    res.status(500).json({ error: error.message });
  }
};

module.exports.listRunningStreams = async (req, res) => {
  if(req.user.admin === false) {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  try {
    const response = await axios.get('https://www.vdocipher.com/api/livestream/running-streams', {
      headers: {
        'Authorization': `Apisecret ${process.env.VDO_API_SECRET}`
      }
    });
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports.endLiveStream = async (req, res) => {
  if(req.user.admin === false) {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  try {
    const response = await axios.post('https://www.vdocipher.com/api/livestream/end-stream', {
      streamId: req.body.streamId
    }, {
      headers: {
        'Authorization': `Apisecret ${process.env.VDO_API_SECRET}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports.addLiveClass = async (req, res) => {
  if(req.user.admin === false) {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  const existingLiveClass = await LiveClass.findOne({ courseId: req.body.courseId });
  if(existingLiveClass) {
    return res.json(existingLiveClass);
  }
  try {
    const liveClass = new LiveClass({
      streamId: req.body.liveStream.streamId,  
      title: req.body.liveStream.title,
      status: req.body.liveStream.status,
      serverKey: req.body.liveStream.serverKey,
      server: req.body.liveStream.server,
      chatMode: req.body.liveStream.chatMode,
      chatSecret: req.body.liveStream.chatSecret,
      createdAt: req.body.liveStream.createdAt,
      streamDuration: req.body.liveStream.streamDuration,
      viewerCount: req.body.liveStream.viewerCount,
      viewerLastUpdate: req.body.liveStream.viewerLastUpdate,
      courseId: req.body.courseId
    });
    const savedLiveClass = await liveClass.save();
    res.json(savedLiveClass);

    (async function pollForActiveStatus() {
      try {
        const intervalId = setInterval(async () => {
          // Fetch stream info from VdoCipher
          const response = await axios.get(`https://www.vdocipher.com/api/livestream/details/${req.body.liveStream.streamId}`);
          
          // Once status is "Streaming Active," update DB and clear polling
          if (response.data && response.data.status === 'Streaming Active') {
            await LiveClass.findOneAndUpdate(
              { streamId: req.body.liveStream.streamId },
              { status: 'Streaming Active' }
            );
            clearInterval(intervalId);
          }
        }, 5000);
      } catch (err) {
        console.error('Polling error:', err);
      }
    })();
    
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}

// find live class by courseId
module.exports.getLiveClass = async (req, res) => {
  try {
    const course_id = req.params.courseId;
    const course = await Courses.findOne({ _id: req.params.courseId });
    if(!course) {
      return res.status(404).json({ error: 'Course not found' });
    }

    const user = await User.findOne({ _id: req.user._id });
    if(!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const findUserFeeStatus = user.feeStatus.find((course) => course.course_id == course_id);

    if (!findUserFeeStatus && !user.admin && !user.status == 'superMentor' && !user.status == 'mentor') {
        return res.redirect(`/courses/enroll/${course_id}`);
    }
    if (findUserFeeStatus && findUserFeeStatus.status == 'pending' && !user.admin && !user.status == 'superMentor' && !user.status == 'mentor') {
        return res.redirect(`/courses/enroll/${course_id}/payfee`);
    }

    const liveClass = await LiveClass.findOne({courseId: req.params.courseId });

    if(!liveClass) {
      return res.redirect(`/classroom/gotoclassroom/${course_id}`);
    }
    
    // create iframe embedded link of live stream for user e.g. https://player.vdocipher.com/live?liveId={STREAM_ID}&token={GENERATED_JWT_TOKEN}
    const token = await generateChatToken({
      userId: user._id.toString(),
      userInfo:{
        username: user.name,
        avatar: user.avatar,
      },
      moderator: user.admin ? true : false || user.status === 'support' || user.status === 'mentor' || user.status === 'instructor' || user.status === 'superMentor'
    }, liveClass.chatSecret);

    //read token and decrypt it to get user info and console log it to see if it is working
    const decoded = jwt.verify(token, liveClass.chatSecret);
    console.log(decoded, 'decoded token');

    const iframeLink = `https://player.vdocipher.com/live-v2?liveId=${liveClass.streamId}&token=${token}`;
    const iframeChatLink = `https://zenstream.chat/?liveId=${liveClass.streamId}&token=${token}`;

    res.render('liveClass', { title: 'Live Class | ' + liveClass.title, iframeLink: iframeLink, request: req, iframeChatLink: iframeChatLink, streamId: liveClass.streamId });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// chat preview
module.exports.chatPreview = async (req, res) => {
  if(req.user.admin == false && req.user.status != 'superMentor') {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  try {
    const liveClass = await LiveClass.findOne({courseId: req.params.courseId });
    if(!liveClass) {
      return res.status(404).json({ error: 'Live class not found' });
    }
    const token = await generateChatToken({
      userId: 'admin',
      userInfo:{
        username: 'admin',
        avatar: 'moderator',
      },
      moderator: req.user.admin ? true : false || req.user.status === 'support' || req.user.status === 'mentor' || req.user.status === 'instructor' || req.user.status === 'superMentor'
    }, liveClass.chatSecret);
    const decoded = jwt.verify(token, liveClass.chatSecret);
    console.log(decoded, 'decoded token');
    const iframeChatLink = `https://zenstream.chat/?liveId=${liveClass.streamId}&token=${token}`;
    res.render('chatPreview', { title: 'Chat Preview | ' + liveClass.title, iframeChatLink: iframeChatLink, streamId: liveClass.streamId });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// delete live class by streamId
module.exports.deleteLiveClass = async (req, res) => {
  if(req.user.admin === false) {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  try {
    const liveClass = await LiveClass.findOneAndDelete({streamId: req.body.streamId });
    res.json(liveClass);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// get live class status by courseId
module.exports.liveClassStatusStream = async (req, res) => {
  res.set({
    'Cache-Control': 'no-cache',
    'Content-Type': 'text/event-stream',
    'Connection': 'keep-alive'
  });
  res.flushHeaders();

  const { courseId } = req.params;
  const intervalId = setInterval(async () => {
    // Query your DB or external service here
    const isActive = await LiveClass.findOne({ courseId: courseId, status: 'Streaming Active' }) ? true : false;
    res.write(`data: ${JSON.stringify({ status: isActive })}\n\n`);
    if (isActive) {
      clearInterval(intervalId);
      res.end();
    }
  }, 5000);

  req.on('close', () => {
    clearInterval(intervalId);
    res.end();
  });
};

// get live class status ended by streamId
module.exports.liveClassStatusEnded = async (req, res) => {
  const { streamId } = req.params;
  const liveClass = await LiveClass.findOne({ streamId: streamId });
  if(!liveClass) {
    return res.status(200).json({ status: true });
  }
  return res.status(200).json({ status: false });
};


// we have slow mode and the ability to pause/unpause chat midway. 

// •⁠  ⁠Chat status can be ACTIVE and PAUSED
// •⁠  ⁠Chat minDelay is default 0 to enable slow mode. 



// curl -X PATCH 'https://dev.vdocipher.com/api/livestream/{STREAM_ID}/chat-config' \
// --header 'Content-Type: application/json' \
// --header 'Authorization: Apisecret API_SECRET' \
// --data '{
//     "chatStatus": "ACTIVE", // optional
//     "chatMsgMinDelay": 5 // optional
// }'



module.exports.chatConfig = async (req, res) => {
  if(req.user.admin === false) {
    return res.status(403).json({ error: 'Unauthorized access' });
  }
  try {
    const response = await axios.patch(`https://dev.vdocipher.com/api/livestream/${req.body.streamId}/chat-config`, {
      chatStatus: req.body.chatStatus || 'ACTIVE',
      chatMsgMinDelay: req.body.chatMsgMinDelay || 0
    }, {
      headers: {
        'Authorization': `Apisecret ${process.env.VDO_API_SECRET}`,
        'Content-Type': 'application/json'
      }
    });
    if(response.data.error) {
      return res.status(400).json({ error: response.data.error });
    }
    if(response.data.chatStatus) {
      return res.status(200).json({ success: true, chatStatus: response.data.chatStatus, chatMsgMinDelay: response.data.chatMsgMinDelay });
    }
  } catch (error) {
    console.log(error)
    res.status(500).json({ error: error.message });
  }
};

