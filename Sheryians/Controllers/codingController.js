const codingQuestionModule = require("../Models/codingQuestion.js");
const submissionDataModel = require("../Models/submissionStorage.js");
const codeSubmissionModel = require("../Models/codeSubmissions.js");
const classroom_content2_5 = require("../Models/classroom_content2.5.js");
const userModel = require("../Models/user.js");
const axios = require("axios");
const rateLimit = require("express-rate-limit");
const formidable = require("formidable");
const JSZip = require("jszip");
const fs = require("fs");
const run = require("../utils/gemini.js");
const Imagekit = require("imagekit");
const { c } = require("compile-run");
const imagekit = new Imagekit({
    publicKey: "public_DQRGKIIs98Gr4cHv0e2XAn73I2U=",
    privateKey: "private_Hk3XPFhDJiyZSBfqdx+bhm537O4=",
    urlEndpoint: "https://ik.imagekit.io/sheryians",
});
let headers = {
    "x-Auth-Token": process.env.JUDGE_API_KEY,
    "Content-Type": "application/json",
};
// let headers = {
//     'x-rapidapi-key': process.env.JUDGE_RAPIDAPI_KEY,
//     'x-rapidapi-host': 'judge0-ce.p.rapidapi.com',
//     'Content-Type': 'application/json'
// }

const decodeBase64 = (base64String) => {
    return Buffer.from(base64String, "base64").toString("utf-8");
};

const parseForm = (req) => {
    return new Promise((resolve, reject) => {
        const form = new formidable.IncomingForm();
        form.keepExtensions = true;

        form.parse(req, (err, fields, files) => {
            if (err) {
                reject(err);
            } else {
                resolve({ fields, files });
            }
        });
    });
};
function convertEmptyStringsToNull(arr) {
    arr.forEach((obj) => {
        for (let key in obj) {
            if (obj[key] === "") {
                obj[key] = null;
            }
        }
    });
    return arr;
}
async function make_C_Zip(lecture, code) {
    const zip = new JSZip();

    // Detect source of code: new schema (code[lang]) or old schema (flat keys)
    const cLangData = lecture.code?.c_cpp || {
        mainCode: lecture.main_code,
        templateCode: lecture.code_template,
    };

    // If template exists, use both main and helper
    const submission = cLangData.templateCode
        ? {
              "main.cpp": cLangData.mainCode,
              "helper.cpp": code,
              compile: `/usr/bin/g++ -o a.out main.cpp helper.cpp`,
              run: `./a.out`,
          }
        : {
              "main.cpp": code,
              compile: `/usr/bin/g++ -o a.out main.cpp`,
              run: `./a.out`,
          };

    // Add files to zip
    for (const [fileName, fileContent] of Object.entries(submission)) {
        zip.file(fileName, fileContent);
    }

    return await zip.generateAsync({ type: "base64" });
}

async function make_Java_Zip(lecture, code) {
    const zip = new JSZip();

    const javaData = lecture.code?.java || {
        mainCode: lecture.main_code,
        templateCode: lecture.code_template,
    };

    const submission = javaData.templateCode
        ? {
              "Main.java": javaData.mainCode,
              "Solution.java": code,
              compile: `#!/bin/bash\njavac --enable-preview --release 17 Main.java Solution.java`,
              run: `#!/bin/bash\njava --enable-preview Main`,
          }
        : {
              "Main.java": code,
              compile: `#!/bin/bash\njavac --enable-preview --release 17 Main.java`,
              run: `#!/bin/bash\njava --enable-preview Main`,
          };

    for (const [fileName, fileContent] of Object.entries(submission)) {
        zip.file(fileName, fileContent);
    }

    return await zip.generateAsync({ type: "base64" });
}

async function make_Python_Zip(lecture, code) {
    const zip = new JSZip();

    const pyData = lecture.code?.python || {
        mainCode: lecture.main_code,
        templateCode: lecture.code_template,
    };

    const submission = pyData.templateCode
        ? {
              "main.py": pyData.mainCode,
              "helper.py": code,
              run: `python3 main.py`,
          }
        : {
              "main.py": code,
              run: `python3 main.py`,
          };

    for (const [fileName, fileContent] of Object.entries(submission)) {
        zip.file(fileName, fileContent);
    }

    return await zip.generateAsync({ type: "base64" });
}

async function make_JavaScript_Zip(lecture, code) {
    const zip = new JSZip();
    const jsData = lecture.code?.javascript || {
        mainCode: lecture.main_code,
        templateCode: lecture.code_template,
    };
    console.log(jsData.templateCode, jsData.mainCode);
    const submission = jsData.templateCode
        ? {
              "main.js": jsData.mainCode,
              "helper.js": code,
              run: `node main.js`,
          }
        : {
              "main.js": code,
              run: `node main.js`,
          };

    for (const [fileName, fileContent] of Object.entries(submission)) {
        zip.file(fileName, fileContent);
    }

    return await zip.generateAsync({ type: "base64" });
}

async function returnSubmissionArray(compilerId, lecture, codeData, code) {
    if (compilerId == 71) {
        const base64zip = await make_Python_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
            };
        });
        return { submissions };
    } else if (compilerId == 62) {
        const base64zip = await make_Java_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
            };
        });
        return { submissions };
    } else if (compilerId == 49) {
        const base64zip = await make_C_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
            };
        });
        return { submissions };
    } else if (compilerId == 63) {
        const base64zip = await make_JavaScript_Zip(lecture, code, codeData);
        const submissions = codeData.mainTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                ...(testcase.maxTime && { cpu_time_limit: testcase.maxTime }),
                ...(testcase.maxMemory && { memory_limit: testcase.maxMemory }),
            };
        });
        return { submissions };
    }
}

async function returnRunSubmissionArray(compilerId, lecture, codeData, code) {
    if (compilerId == 71) {
        const base64zip = await make_Python_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
                // expected_output:Buffer.from(testcase.stdout).toString('base64'),
            };
        });
        return submissions;
    } else if (compilerId == 62) {
        const base64zip = await make_Java_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;
    } else if (compilerId == 49) {
        const base64zip = await make_C_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;
    } else if (compilerId == 63) {
        const base64zip = await make_JavaScript_Zip(lecture, code, codeData);
        const submissions = codeData.sampleTestcases.map((testcase) => {
            return {
                additional_files: base64zip,
                language_id: 89,
                ...(testcase.stdin ? { stdin: Buffer.from(testcase.stdin).toString("base64") } : {}),
                ...(testcase.stdout ? { expected_output: Buffer.from(testcase.stdout).toString("base64") } : {}),
            };
        });
        return submissions;
    }
}

const limiter = rateLimit({
    windowMs: 10 * 1000, // 30 sec
    max: 1, // Limit each IP to 1 requests per windowMs
    message: "Too many requests from this IP, please try again after 15 minutes",
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    keyGenerator: function (req) {
        return req.get("do-connecting-ip") || req.ip;
    },
});

module.exports.getAddCoding = (req, res) => {
    return res.render("addCoding.ejs", { questions: null });
};

module.exports.postAddCoding = async (req, res) => {
    try {
        const { fields, files } = await parseForm(req);
        var result;
        if (files.file) {
            const filePath = files.file[0].filepath;
            result = await imagekit.upload({
                file: fs.createReadStream(filePath),
                fileName: files.file[0].newFilename,
            });
        }
        let { id, inputFormat, outputFormat, hint, title, description, mainTestcase, sampleTestcase, constrains, topic } = fields;
        if (!title || !description || !sampleTestcase || !mainTestcase || !topic || !constrains) {
            return res.status(400).send({ error: "All fields are required." });
        }
        let mainTestcases = convertEmptyStringsToNull(JSON.parse(mainTestcase[0]));
        let sampleTestcases = convertEmptyStringsToNull(JSON.parse(sampleTestcase[0]));
        constrains = JSON.parse(constrains[0]);

        const question = new codingQuestionModule({
            id: id[0],
            title: title[0],
            description: description[0],
            sampleTestcases: sampleTestcases,
            mainTestcases: mainTestcases,
            topic: topic[0],
            hint: hint[0],
            inputFormat: inputFormat[0],
            outputFormat: outputFormat[0],
            constrains: constrains,
            image: result ? result.url : "",
        });
        await question.save();
        res.status(200).json({ message: "Success" });
    } catch (err) {
        console.log(err);
        res.send(err);
    }
};

module.exports.checkCodingID = async (req, res) => {
    try {
        const data = await codingQuestionModule.find({ id: req.body.id });
        res.status(200).json({ data });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

module.exports.getCodingQuestions = async (req, res) => {
    const questions = await codingQuestionModule.find();
    res.render("codingQuestions.ejs", { questions });
};

module.exports.getSpecificCodingQuestions = async (req, res) => {
    const questions = await codingQuestionModule.findById(req.params.id);
    res.render("addCoding.ejs", { questions });
};

module.exports.saveCoding = async (req, res) => {
    let language;
    if (req.body.compilerId == 71) {
        language = "python";
    } else if (req.body.compilerId == 62) {
        language = "java";
    } else if (req.body.compilerId == 49) {
        language = "c_cpp";
    } else if (req.body.compilerId == 63) {
        language = "javascript";
    }
    const submissionData = await submissionDataModel.findOne({ userId: req.user._id, questionId: req.body.lectureId, language });
    if (submissionData) {
        submissionData.code = req.body.code;
        await submissionData.save();
    } else {
        const submission = new submissionDataModel({
            userId: req.user._id,
            questionId: req.body.lectureId,
            language: language,
            code: req.body.code,
        });
        await submission.save();
    }
};

module.exports.updateCodingQuestions = async (req, res) => {
    try {
        const { fields, files } = await parseForm(req);
        var result;
        if (files.file) {
            const filePath = files.file[0].filepath;
            result = await imagekit.upload({
                file: fs.createReadStream(filePath),
                fileName: files.file[0].newFilename,
            });
        }
        let { id, inputFormat, outputFormat, hint, title, description, mainTestcase, sampleTestcase, constrains, topic } = fields;
        if (!title || !description || !sampleTestcase || !mainTestcase || !topic || !constrains) {
            return res.status(400).send({ error: "All fields are required." });
        }
        let mainTestcases = convertEmptyStringsToNull(JSON.parse(mainTestcase[0]));
        let sampleTestcases = convertEmptyStringsToNull(JSON.parse(sampleTestcase[0]));
        constrains = JSON.parse(constrains[0]);
        const question = await codingQuestionModule.findById(req.params.id);
        question.id = id[0];
        (question.title = title[0]), (question.description = description[0]), (question.sampleTestcases = sampleTestcases), (question.mainTestcases = mainTestcases), (question.inputFormat = inputFormat[0]), (question.outputFormat = outputFormat[0]), (question.hint = hint[0]), (question.topic = topic[0]), (question.constrains = constrains), (question.image = result ? result.url : question.image);

        await question.save();
        res.status(200).json({ message: "Success" });
    } catch (err) {
        console.log(err);
        res.send(err);
    }
};

module.exports.getQuestions = async (req, res) => {
    try {
        const { title } = req.body;
        if (!title) return res.status(400).json({ message: "Title Missing" });
        const regexPattern = new RegExp(title, "i"); // 'i' for case-insensitive
        const questions = await codingQuestionModule.find({ title: { $regex: regexPattern } }).select("id title");
        res.status(200).json({ questions });
    } catch (err) {
        console.log(err);
        res.status(500).json({ message: err.message });
    }
};

module.exports.deleteCodingQuestions = async (req, res) => {
    const question = await codingQuestionModule.findByIdAndDelete(req.params.id);
    res.redirect("/coding");
};

module.exports.reviewCode = async (req, res) => {
    try {
        const { code } = req.body;
        const problem = await codingQuestionModule.findById(req.body.problemId);

        const prompt = `I have written a solution to a problem, and I would like a review of my code. you rate the code internally on a scale of 1-10 if the rating is greater than 8 you dont suggest much changes and appricate the user and show the rating too Please assess its correctness, efficiency (time and space complexity), and suggest any potential improvements but dont provide actual code.
        Problem Statement: Title: ${problem.title} Description: ${problem.description} Constraints: ${problem.constrains} Explaination: ${problem.explaination}
        My Solution: ${code}

        Review Request:
        1. Is my code logically correct for the given problem?
        2. How can I improve the time/space complexity?
        3. Are there any optimizations or best practices I should apply except error handling, readability and variable names?
        4. Provide better solutions if necessary and explain why they are better.
        `;

        const response = await run(prompt);
        res.status(200).json({ review: response });

        const { lectureId, courseId } = req.body;

        const user = await userModel.findById(req.user._id);
        const classroom_track = user.classroom_track.find((id) => id.courseId.toString() == courseId.toString());
        const classroom_track_lecture = classroom_track.completedLectures.find((data) => data.lectureId.toString() == lectureId.toString());
        classroom_track_lecture.freezeMarks = true;

        await submissionDataModel.findOneAndUpdate({ userId: req.user._id, questionId: lectureId }, { seenSolution: true });
        await user.save();
    } catch (err) {
        console.log(err);
        res.status(500).json({ message: "Internal Server Error" });
    }
};

module.exports.runCode = async (req, res) => {
    try {
        const { lectureId, moduleId, code, compilerId } = req.body;
        if (!code || !lectureId || !moduleId) return res.status(400).json({ message: "Fields Missing" });
        console.log(req.body);
        const result = await classroom_content2_5.findOne({ "modules.lectures._id": lectureId }).lean();
        if (!result) return res.status(400).json({ message: "Lecture Not Found" });

        const lecture = result.modules.find((modID) => modID._id.toString() == moduleId.toString()).lectures.find((lecID) => lecID._id.toString() == lectureId.toString());
        if (!lecture) return res.status(400).json({ message: "Lecture Not Found" });

        const problem = await codingQuestionModule.findOne({ id: lecture.problemId }).lean();
        if (!problem) return res.status(400).json({ message: "Coding Question Not Found" });
        const submissions = await returnRunSubmissionArray(compilerId, lecture, problem, code);
        const submissionRoute = submissions.map((data) => {
            const options = {
                method: "POST",
                url: `${process.env.JUDGE_URL}/submissions`,
                params: {
                    base64_encoded: "true",
                    wait: "false",
                    fields: "*",
                },
                headers: headers,
                data: data,
            };

            return axios
                .request(options)
                .then((response) => response.data)
                .catch((error) => {
                    console.error("Error in submission:", error);
                    return null; // or handle the error as needed
                });
        });

        const response = await Promise.all(submissionRoute);
        const tokens = response.map((data) => data.token);

        res.status(200).json({ tokens: tokens });

        const submissionData = await submissionDataModel.findOne({ userId: req.user._id, questionId: lectureId });
        let language;

        if (lecture.compilerId == 71) {
            language = "python";
        } else if (lecture.compilerId == 62) {
            language = "java";
        } else if (lecture.compilerId == 49) {
            language = "c";
        } else if (lecture.compilerId == 63) {
            language = "javascript";
        }

        if (submissionData) {
            submissionData.code = code;
            submissionData.isExecuting = true;
            await submissionData.save();
        } else {
            const submission = new submissionDataModel({
                userId: req.user._id,
                questionId: lectureId,
                language: language,
                code: code,
            });
            await submission.save();
        }
    } catch (err) {
        console.log(err);
        res.status(500).json({ message: "Internal Error", error: err.message });
    }
};

module.exports.submitCode = async (req, res) => {
    try {
        const { lectureId, moduleId, code, compilerId } = req.body;
        if (!lectureId || !moduleId || !code) return res.status(400).json({ message: "Incomplete Fields" });

        const result = await classroom_content2_5.findOne({ "modules.lectures._id": lectureId }).lean();
        if (!result) return res.status(400).json({ message: "Lecture Not Found" });

        const lecture = result.modules.find((modID) => modID._id.toString() == moduleId.toString()).lectures.find((lecID) => lecID._id.toString() == lectureId.toString());
        if (!lecture) return res.status(400).json({ message: "Lecture Not Found" });

        const problem = await codingQuestionModule.findOne({ id: lecture.problemId });
        if (!problem) return res.status(400).json({ message: "Coding Question Not Found" });

        const submissions = await returnSubmissionArray(compilerId, lecture, problem, code);
        const { data } = await axios.request({
            method: "POST",
            url: `${process.env.JUDGE_URL}/submissions/batch`,
            params: {
                base64_encoded: "true",
            },
            headers: headers,
            data: submissions,
        });
        res.status(200).json({ tokens: data });
        const submissionData = await submissionDataModel.findOne({ userId: req.user._id, questionId: lectureId });
        let language;

        if (lecture.compilerId == 71) {
            language = "python";
        } else if (lecture.compilerId == 62) {
            language = "java";
        } else if (lecture.compilerId == 49) {
            language = "c";
        } else if (lecture.compilerId == 63) {
            language = "javascript";
        }

        if (submissionData) {
            submissionData.code = code;
            await submissionData.save();
        } else {
            const submission = new submissionDataModel({
                userId: req.user._id,
                questionId: lectureId,
                language: language,
                code: code,
            });
            await submission.save();
        }
    } catch (err) {
        console.log("Error in submitting", err.message);
        res.status(500).json({ message: "Internal Error", error: err });
    }
};

module.exports.checkStatus = async (req, res) => {
    try {
        const { data } = await axios.request({
            method: "GET",
            url: `${process.env.JUDGE_URL}/submissions/batch/`,
            params: {
                tokens: req.body.token.join(","),
                base64_encoded: "true",
                fields: "*",
            },
            headers: headers,
        });
        const newData = data.submissions.map((submission) => ({
            time: submission.time,
            memory: submission.memory,
            stderr: submission.stderr ? Buffer.from(submission.stderr, "base64").toString("utf-8") : submission.stderr,
            message: submission.message ? Buffer.from(submission.message, "base64").toString("utf-8") : submission.message,
            status: submission.status,
            compile_error: submission.compile_output ? Buffer.from(submission.compile_output, "base64").toString("utf-8") : submission.compile_output,
            error: submission.error,
            // stdout: submission.stdout ? Buffer.from(submission.stdout, 'base64').toString('utf-8') : submission.stdout,
        }));
        // console.log(newData)
        res.status(200).json({ data: newData });

        if (data.submissions.every((state) => state.status.id != 1 && state.status.id != 2)) {
            updateSubmissionAndStatus(req.body.language, req.user.id, data.submissions, req.body.lectureId, req.body.courseId);
        }
    } catch (err) {
        console.log(err);
        console.log(err.message);
    }
};

module.exports.checkRunStatus = async (req, res) => {
    try {
        const checkRoute = req.body.token.map((token) =>
            axios.request({
                method: "GET",
                url: `${process.env.JUDGE_URL}/submissions/${token}`,
                params: {
                    base64_encoded: "true",
                    fields: "*",
                },
                headers: headers,
            })
        );
        const response = await Promise.all(checkRoute);
        const data = response.map((deta) => ({
            language_id: deta.data.language_id,
            stdin: deta.data.stdin ? Buffer.from(deta.data.stdin, "base64").toString("utf-8") : deta.data.stdin,
            expected_output: deta.data.expected_output ? Buffer.from(deta.data.expected_output, "base64").toString("utf-8") : deta.data.expected_output,
            stdout: deta.data.stdout ? Buffer.from(deta.data.stdout, "base64").toString("utf-8") : deta.data.stdout,
            time: deta.data.time,
            memory: deta.data.memory,
            stderr: deta.data.stderr ? Buffer.from(deta.data.stderr, "base64").toString("utf-8") : deta.data.stderr,
            token: deta.data.token,
            compile_error: deta.data.compile_output ? Buffer.from(deta.data.compile_output, "base64").toString("utf-8") : deta.data.compile_output,
            message: deta.data.message ? Buffer.from(deta.data.message, "base64").toString("utf-8") : deta.data.message,
            status: deta.data.status,
        }));
        console.log(data);
        res.status(200).json({ data });
    } catch (err) {
        console.log(err.message);
    }
};

module.exports.getSubmissions = async (req, res) => {
    try {
        const submissions = await submissionDataModel.findOne({ userId: req.user._id, questionId: req.body.lectureId });
        const codeSubmissions = await codeSubmissionModel.find({ submissionStorageId: submissions._id }).sort({ createdAt: -1 });
        if (codeSubmissions.length != 0) {
            res.status(200).json({ submissions: codeSubmissions });
        } else {
            res.status(200).json({ submissions: [] });
        }
    } catch (err) {
        console.log(err.message);
        res.status(500).json({ message: "Internal error", msg: err.message });
    }
};

module.exports.getSolution = async (req, res) => {
    try {
        const { lectureId, moduleId, courseId } = req.body;
        const result = await classroom_content2_5.findOne({ "modules._id": moduleId, "modules.lectures._id": lectureId });

        const lecture = result.modules.find((modID) => modID._id.toString() === moduleId.toString()).lectures.find((lecID) => lecID._id.toString() === lectureId.toString());

        const modeMap = {
            62: "java",
            71: "python",
            49: "c_cpp",
            63: "javascript",
        };

        let code = {};
        if (lecture.code instanceof Map || lecture.code?.entries) {
            // If it's a Map
            code = Object.fromEntries(Array.from(lecture.code.entries()).map(([lang, val]) => [lang, val.solutionCode]));
        } else if (typeof lecture.code === "object") {
            // If it's a plain object
            code = Object.fromEntries(Object.entries(lecture.code).map(([lang, val]) => [lang, val.solutionCode]));
        } else if (lecture.compilerId && lecture.solution_code) {
            // Fallback: single solutionCode with compilerId
            const language = modeMap[lecture.compilerId] || "unknown";
            code[language] = lecture.solution_code;
        }

        // Send the solution code map
        res.status(200).json({ code });
        // ✅ Mark lecture as seen
        const user = await userModel.findById(req.user._id);
        const classroom_track = user.classroom_track.find((track) => track.courseId.toString() === courseId.toString());
        const classroom_track_lecture = classroom_track?.completedLectures.find((l) => l.lectureId.toString() === lectureId.toString());

        if (classroom_track_lecture) {
            classroom_track_lecture.freezeMarks = true;
        }

        await submissionDataModel.findOneAndUpdate({ userId: req.user._id, questionId: lectureId }, { seenSolution: true });

        await user.save();
    } catch (err) {
        console.error(err.message);
        res.status(500).json({ message: "Internal error", msg: err.message });
    }
};

async function updateSubmissionAndStatus(language, userId, submissionArray, lectureId, courseId, code) {
    try {
        const submissionData = await submissionDataModel.findOne({ userId: userId, questionId: lectureId });
        const casesArray = submissionArray.map((e) => {
            return {
                stdin: e.stdin,
                stdout: e.stdout,
                expectedOutput: e.expected_output,
                passed: e.status.description == "Accepted" ? true : false,
                executionTime: e.time,
                memoryUsage: e.memory,
                error: e.message ? e.message : "",
            };
        });
        if (submissionData) {
            codeSubmit = await new codeSubmissionModel({
                testcases: casesArray,
                date: new Date(),
                language: language,
                code: submissionData.code,
                submissionStorageId: submissionData._id,
            });
            submissionData.status = submissionData.status == "failed" ? (submissionArray.every((state) => state.status.id == 3) ? "passed" : "failed") : "passed";
            await submissionData.save();
            await codeSubmit.save();
        }

        const user = await userModel.findById(userId);
        const passedTestcases = submissionArray.filter((data) => data.status.id == 3).length;
        const passedPercentage = Math.floor((passedTestcases / submissionArray.length) * 100);

        const classroom_track = user.classroom_track.find((id) => id.courseId.toString() == courseId.toString());
        const classroom_track_lecture = classroom_track.completedLectures.find((data) => data.lectureId.toString() == lectureId.toString());
        if (classroom_track_lecture.freezeMarks) return;

        if ((classroom_track_lecture.percentageCompleted == 0 && passedPercentage == 0) || (passedPercentage == 0 && classroom_track_lecture.percentageCompleted == -1)) {
            classroom_track_lecture.percentageCompleted = -1;
        } else if (classroom_track_lecture.percentageCompleted < passedPercentage) {
            classroom_track_lecture.percentageCompleted = passedPercentage;
        }
        await user.save();
    } catch (err) {
        console.log(err);
    }
}

