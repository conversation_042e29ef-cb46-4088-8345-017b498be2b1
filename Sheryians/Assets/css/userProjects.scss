// Import common variables and styles
@import "common";

// Sheryians Color System
$bg-primary: #0c0c0c; // Main dark background
$bg-secondary: #1e1e1e; // Secondary darker background
$text-primary: #ffffff; // Primary white text
$text-secondary: #b0b0b0; // Secondary gray text
$border-color: #333333; // Border color
$accent-color: #24cfa6; // Sheryians accent green

// Color variables specific to user projects
$success-color: #24cfa6;
$warning-color: #f39c12;
$danger-color: #e74c3c;
$info-color: #3498db;

.user-container {
    margin: 0 auto;
    width: 90%;
    padding: 2rem;
    background: $bg-primary;
    min-height: 100vh;

    @media (max-width: 768px) {
        padding: 1rem;
    }
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: $bg-secondary;
    border-radius: 12px;
    border: 1px solid $border-color;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .header-content {
        flex: 1;

        h1 {
            color: $text-primary;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;

            @media (max-width: 768px) {
                font-size: 2rem;
            }
        }

        p {
            color: $text-secondary;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0;
        }
    }

    .header-actions {
        .submit-new-btn {
            background: linear-gradient(135deg, $accent-color, lighten($accent-color, 10%));
            color: $bg-primary;
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;

            .btn-icon {
                font-size: 1.2rem;
                font-weight: bold;
            }

            

            &:active {
                transform: translateY(0);
            }
        }
    }
}

// Stats Grid
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

.stat-card {
    background: $bg-secondary;
    border: 1px solid $border-color;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: $accent-color;
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    

    &.total::before {
        background: $accent-color;
    }
    &.pending::before {
        background: $warning-color;
    }
    &.approved::before {
        background: $success-color;
    }
    &.rejected::before {
        background: $danger-color;
    }

    .stat-icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .stat-content {
        flex: 1;

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: $text-primary;
            margin-bottom: 0.25rem;

            @media (max-width: 768px) {
                font-size: 1.5rem;
            }
        }

        .stat-label {
            color: $text-secondary;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
}

// Controls Section
.controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: $bg-secondary;
    border-radius: 12px;
    border: 1px solid $border-color;
    gap: 1rem;

    @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 1rem;

        label {
            color: $text-primary;
            font-weight: 500;
            white-space: nowrap;
        }

        .filter-dropdown {
            background: $bg-primary;
            color: $text-primary;
            border: 1px solid $border-color;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;

            
        }
    }

    .search-group {
        position: relative;
        flex: 1;
        max-width: 300px;

        .search-input {
            width: 100%;
            background: $bg-primary;
            color: $text-primary;
            border: 1px solid $border-color;
            border-radius: 8px;
            padding: 0.75rem 2.5rem 0.75rem 1rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;

            &::placeholder {
                color: $text-secondary;
            }

            
        }

        .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: $text-secondary;
            pointer-events: none;
        }
    }
}

// Projects Table
.projects-table-container {
    background: $bg-secondary;
    border-radius: 12px;
    border: 1px solid $border-color;
    overflow: hidden;
}

.projects-table {
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2rem;
        border-bottom: 1px solid $border-color;
        background: rgba($accent-color, 0.05);

        h3 {
            color: $text-primary;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .table-info {
            color: $text-secondary;
            font-size: 0.9rem;
        }
    }

    .table-content {
        overflow-x: auto;

        table {
            width: 100%;
            border-collapse: collapse;

            thead {
                background: rgba($bg-primary, 0.5);

                th {
                    color: $text-primary;
                    font-weight: 600;
                    font-size: 0.9rem;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    padding: 1rem 1.5rem;
                    text-align: left;
                    border-bottom: 1px solid $border-color;

                    &:first-child {
                        padding-left: 2rem;
                    }

                    &:last-child {
                        padding-right: 2rem;
                    }
                }
            }

            tbody {
                .project-row {
                    border-bottom: 1px solid $border-color;
                    transition: all 0.3s ease;

                    

                    &.pending {
                        border-left: 4px solid $warning-color;
                    }

                    &.approved {
                        border-left: 4px solid $success-color;
                    }

                    &.rejected {
                        border-left: 4px solid $danger-color;
                    }

                    td {
                        padding: 1.5rem;
                        vertical-align: top;

                        &:first-child {
                            padding-left: 2rem;
                        }

                        &:last-child {
                            padding-right: 2rem;
                        }
                    }
                }
            }
        }
    }
}

.project-details {
    .project-title {
        color: $text-primary;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0 0 0.75rem 0;
    }

    .project-links {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .project-link {
            color: $accent-color;
            text-decoration: none;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0;
            transition: all 0.3s ease;

            .link-icon {
                font-size: 0.8rem;
            }

            

            &.github {
                color: $info-color;

                
            }
        }
    }
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.pending {
        background: rgba($warning-color, 0.1);
        color: $warning-color;
        border: 1px solid rgba($warning-color, 0.3);
    }

    &.approved {
        background: rgba($success-color, 0.1);
        color: $success-color;
        border: 1px solid rgba($success-color, 0.3);
    }

    &.rejected {
        background: rgba($danger-color, 0.1);
        color: $danger-color;
        border: 1px solid rgba($danger-color, 0.3);
    }
}

.date-info {
    .date-text {
        color: $text-primary;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .time-text {
        color: $text-secondary;
        font-size: 0.8rem;
    }
}

.review-info {
    .review-date {
        color: $text-primary;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .review-notes-preview {
        color: $accent-color;
        font-size: 0.8rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;

        

        .notes-icon {
            font-size: 0.7rem;
        }
    }

    .no-notes {
        color: $text-secondary;
        font-size: 0.8rem;
        font-style: italic;
    }
}

.pending-review {
    color: $text-secondary;
    font-size: 0.9rem;
    font-style: italic;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid $border-color;
        background: $bg-primary;
        color: $text-secondary;
        text-decoration: none;
        transition: all 0.3s ease;
        cursor: pointer;

        .btn-icon {
            font-size: 0.9rem;
        }

        
    }
}

// Project Modal
.project-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;

    &.show {
        opacity: 1;
    }

    .modal-content {
        background: $bg-secondary;
        border-radius: 12px;
        border: 1px solid $border-color;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid $border-color;

            h3 {
                color: $text-primary;
                font-size: 1.25rem;
                font-weight: 600;
                margin: 0;
            }

            .modal-close {
                background: none;
                border: none;
                color: $text-secondary;
                font-size: 1.5rem;
                cursor: pointer;
                transition: color 0.3s ease;

                
            }
        }

        .modal-body {
            padding: 2rem;

            .project-status {
                margin-bottom: 1.5rem;
            }

            .project-info-grid {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 1.5rem;

                .info-item {
                    label {
                        display: block;
                        color: $text-secondary;
                        font-size: 0.9rem;
                        font-weight: 500;
                        margin-bottom: 0.25rem;
                    }

                    span,
                    a {
                        color: $text-primary;
                        font-size: 0.9rem;
                    }

                    a {
                        color: $accent-color;
                        text-decoration: none;

                        
                    }
                }
            }

            .review-feedback {
                h4 {
                    color: $text-primary;
                    font-size: 1rem;
                    font-weight: 600;
                    margin-bottom: 0.75rem;
                }

                .feedback-content {
                    background: rgba($bg-primary, 0.5);
                    border-radius: 8px;
                    padding: 1rem;
                    border-left: 4px solid $accent-color;
                    color: $text-secondary;
                    line-height: 1.5;
                    font-style: italic;
                }
            }
        }
    }
}

// Loading State
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem;
    color: $text-secondary;

    .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba($accent-color, 0.1);
        border-top: 3px solid $accent-color;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    p {
        font-size: 1.1rem;
        margin: 0;
    }
}

// Empty State
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    h3 {
        color: $text-primary;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    p {
        color: $text-secondary;
        font-size: 1rem;
        margin-bottom: 2rem;
        max-width: 400px;
    }

    .submit-new-btn {
        background: linear-gradient(135deg, $accent-color, lighten($accent-color, 10%));
        color: $bg-primary;
        padding: 1rem 2rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;

        .btn-icon {
            font-size: 1.2rem;
            font-weight: bold;
        }

        

        &:active {
            transform: translateY(0);
        }
    }
}

// Notification
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;

    &.show {
        transform: translateX(0);
    }

    &.success {
        background: rgba($success-color, 0.1);
        color: $success-color;
        border: 1px solid rgba($success-color, 0.3);
    }

    &.error {
        background: rgba($danger-color, 0.1);
        color: $danger-color;
        border: 1px solid rgba($danger-color, 0.3);
    }
}

// Animations
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// Responsive Design
@media (max-width: 1024px) {
    .projects-table .table-content table {
        font-size: 0.9rem;

        th,
        td {
            padding: 1rem;
        }
    }
}

@media (max-width: 768px) {
    .user-header {
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;

        .stat-icon {
            font-size: 1.5rem;
        }

        .stat-content .stat-number {
            font-size: 1.5rem;
        }
    }

    .controls-section {
        padding: 1rem;
    }

    .projects-table .table-content {
        .project-details .project-links {
            .project-link {
                font-size: 0.8rem;
            }
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.25rem;

            .action-btn {
                width: 32px;
                height: 32px;
            }
        }
    }

    .project-modal .modal-content {
        width: 95%;
        margin: 1rem;

        .modal-body {
            padding: 1.5rem;
        }
    }
}
