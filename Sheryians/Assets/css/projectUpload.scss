// Project Upload Page Styles
// Matches the Sheryians design system with black theme and accent colors

@import "common.scss";

html,
body {
    height: 100%;
    width: 100%;
    font-size: 16px !important;
}

body {
    background: var(--background-color);
    color: var(--text-color);
    font-family: "Neue<PERSON>achina", "<PERSON>", sans-serif;
}

// Main container
#main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;

    .container {
        width: 100%;
        max-width: 35rem;
        padding: 0 1rem;

        @media (max-width: 600px) {
            max-width: 90%;
            padding: 0 0.5rem;
        }
    }
}

// Project form card
.project-form {
    background: var(--ui-element-color);
    border: 1px solid var(--ui-element-ternary-color);
    border-radius: 8px;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    @media (max-width: 600px) {
        padding: 1.5rem;
        border-radius: 6px;
    }
}

// Header section
.form-header {
    text-align: center;
    margin-bottom: 2rem;

    h1 {
        font-size: 2.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-color);
        font-family: "NeueMachina", sans-serif;

        @media (max-width: 600px) {
            font-size: 1.8rem;
        }
    }

    p {
        color: var(--sub-text-color);
        font-size: 0.95rem;
        font-weight: 300;
        line-height: 1.5;
    }
}

// Form groups
.form-group {
    margin-bottom: 1.5rem;

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.85rem;
        font-weight: 500;
        color: var(--sub-text-color);
        text-transform: capitalize;
    }

    .required {
        color: var(--accent-color);
        margin-left: 2px;
    }
}

// Input fields matching your theme
.form-input {
    width: 100%;
    background-color: var(--Shark);
    border: 1px solid transparent;
    color: var(--text-color);
    padding: 0.8rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 400;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);

    &::placeholder {
        color: var(--sub-text-color);
        opacity: 0.7;
    }

    &:focus {
        outline: none;
        border-color: var(--accent-color);
        background-color: var(--ui-element-ternary-color);
        box-shadow: 0 0 0 2px rgba(36, 207, 166, 0.1);
    }

    &:hover {
        border-color: var(--form-input-border);
    }

    // Error state
    &.error {
        border-color: var(--error-color) !important;
        background-color: rgba(136, 39, 39, 0.1) !important;
    }

    // Success state
    &.success {
        border-color: var(--accent-color);
        background-color: rgba(36, 207, 166, 0.05);
    }
}

// Textarea specific styles
textarea.form-input {
    min-height: 100px;
    resize: vertical;
}

// Error and success messages
.error-message,
.success-message {
    font-size: 0.75rem;
    margin-top: 0.3rem;
    padding: 0.3rem 0;
    display: none;

    &.show {
        display: block;
    }
}

.error-message {
    color: var(--error-color);

    &::before {
        content: "⚠ ";
        margin-right: 0.2rem;
    }
}

.success-message {
    color: var(--accent-color);

    &::before {
        content: "✓ ";
        margin-right: 0.2rem;
    }
}

// Submit button
.submit-btn {
    width: 100%;
    background: var(--accent-color);
    color: var(--black);
    border: none;
    padding: 0.9rem 1.5rem;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: 600;
    font-family: "NeueMachina", sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;

}
.viewSubmissionBtn {
    width: 100%;
    background: var(--fuscous-gray);
    color: var(--white);
    border: none;
    padding: 0.9rem 1.5rem;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: 600;
    font-family: "NeueMachina", sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

// Loading state
.loading {
    position: relative;
    color: transparent !important;

    &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 16px;
        height: 16px;
        border: 2px solid var(--black);
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

// Help text
.help-text {
    font-size: 0.75rem;
    color: var(--sub-text-color);
    margin-top: 0.3rem;
    line-height: 1.4;

    a {
        color: var(--link-color);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

// Notification styles
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 1000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    min-width: 300px;

    &.show {
        opacity: 1;
        transform: translateX(0);
    }

    &.success {
        background: rgba(36, 207, 166, 0.1);
        border: 1px solid var(--accent-color);
        color: var(--accent-color);
    }

    &.error {
        background: rgba(136, 39, 39, 0.1);
        border: 1px solid var(--error-color);
        color: var(--error-color);
    }

    @media (max-width: 600px) {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}

// Back link
.back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--sub-text-color);
    text-decoration: none;
    font-size: 0.9rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;

    &:hover {
        color: var(--accent-color);
    }

    &::before {
        content: "←";
        font-size: 1.1rem;
    }
}

// Responsive adjustments
@media (max-width: 600px) {
    #main {
        padding: 1rem 0;
    }

    .form-group {
        margin-bottom: 1.2rem;
    }

    .form-input {
        padding: 0.7rem 0.8rem;
        font-size: 0.85rem;
    }

    .submit-btn {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
    }
}

// Animation for form appearance
.project-form {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Focus indicators for accessibility
.form-input:focus,
.submit-btn:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

// Dark scrollbar
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--ui-element-color);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 3px; 
}

::-webkit-scrollbar-thumb:hover {
    background: var(--Lochinvar);
}
