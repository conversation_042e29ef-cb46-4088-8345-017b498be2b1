/* Hire From Us Status Page Styles */

#view1 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

#view1 .text h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

#view1 .text h1 span {
  color: var(--text-color);
}

#view1 .text h1 .text-highlight {
  color: var(--accent-color);
}

#view1 .text p {
  font-size: clamp(1rem, 2vw, 1.3rem);
  color: var(--sub-text-color);
  max-width: 600px;
  margin: 0 auto;
}

#view2 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

#view2 .top {
  text-align: center;
  margin-bottom: 4rem;
}

#view2 .top h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

#view2 .top h1 span {
  color: var(--accent-color);
}

#view2 .top svg {
  width: 24px;
  height: 24px;
}

.hire-intro {
  margin-bottom: 4rem;
}

.intro-card {
  background: var(--ui-element-color);
  border-radius: 1rem;
  padding: 3rem;
  text-align: center;
  border: 1px solid var(--form-input-border);
  max-width: 800px;
  margin: 0 auto;
}

.intro-icon {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
}

.intro-card h2 {
  font-size: 2rem;
  color: var(--text-color);
  margin-bottom: 1rem;
  font-weight: 600;
}

.intro-card p {
  color: var(--sub-text-color);
  font-size: 1.1rem;
  line-height: 1.7;
}

.contact-section {
  margin-bottom: 4rem;
}

.contact-section h2 {
  text-align: center;
  font-size: 2.2rem;
  color: var(--text-color);
  margin-bottom: 2rem;
  font-weight: 600;
}

.contact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.contact-card {
  background: var(--ui-element-color);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  border: 1px solid var(--form-input-border);
  transition: transform 0.3s ease, border-color 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-3px);
  border-color: var(--accent-color);
}

.contact-icon {
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.contact-card h3 {
  font-size: 1.5rem;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.contact-card p {
  color: var(--sub-text-color);
  margin-bottom: 1.5rem;
}

.contact-link {
  display: inline-block;
  text-decoration: none;
}

.services-section {
  margin-bottom: 4rem;
}

.services-section h2 {
  text-align: center;
  font-size: 2.2rem;
  color: var(--text-color);
  margin-bottom: 3rem;
  font-weight: 600;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.service-item {
  background: var(--ui-element-color);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid var(--form-input-border);
  transition: transform 0.3s ease, border-color 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.service-item:hover {
  transform: translateY(-3px);
  border-color: var(--accent-color);
}

.service-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background: rgba(36, 207, 166, 0.1);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-icon i {
  font-size: 1.5rem;
  color: var(--accent-color);
}

.service-content h3 {
  font-size: 1.25rem;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.service-content p {
  color: var(--sub-text-color);
  font-size: 0.95rem;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  #view1 .text h1 {
    font-size: 2.5rem;
  }

  #view2 .top h1 {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .intro-card {
    padding: 2rem;
  }

  .contact-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }

  .service-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .service-icon {
    align-self: center;
  }
}

@media (max-width: 480px) {
  #view1 .text h1 {
    font-size: 2rem;
  }

  #view2 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .intro-card {
    padding: 1.5rem;
  }

  .service-item {
    padding: 1.5rem;
  }

  .contact-card {
    padding: 1.5rem;
  }
}


