* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #24cfa6 0%, #428e7c 100%);
    min-height: 100vh;
}

.hire-status-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    width: 100%;
    max-width: 1000px;
}

.header-section {
    text-align: center;
    margin-bottom: 3rem;
}

.logo {
    width: 120px;
    height: auto;
    margin-bottom: 1rem;
}

.main-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #24cfa6, #428e7c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2rem;
    color: #718096;
    margin-bottom: 2rem;
}

.status-section {
    margin-bottom: 3rem;
}

.status-card {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.status-icon {
    font-size: 3rem;
    color: #24cfa6;
    margin-bottom: 1rem;
}

.status-card h2 {
    font-size: 1.8rem;
    color: #2d3748;
    margin-bottom: 1rem;
}

.status-card p {
    color: #4a5568;
    font-size: 1.1rem;
    line-height: 1.7;
}

.contact-section {
    margin-bottom: 3rem;
}

.contact-section h2 {
    text-align: center;
    font-size: 2.2rem;
    color: #2d3748;
    margin-bottom: 2rem;
}

.contact-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid transparent;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: #24cfa6;
}

.contact-icon {
    font-size: 2.5rem;
    color: #24cfa6;
    margin-bottom: 1rem;
}

.contact-card h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.contact-card p {
    color: #718096;
    margin-bottom: 1rem;
}

.contact-link {
    display: inline-block;
    color: #24cfa6;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.contact-link:hover {
    background: #24cfa6;
    color: white;
    transform: scale(1.05);
}

.services-section {
    margin-bottom: 3rem;
}

.services-section h2 {
    text-align: center;
    font-size: 2.2rem;
    color: #2d3748;
    margin-bottom: 2rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-item {
    background: white;
    border-radius: 12px;
    padding: 1.8rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.service-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(36, 207, 166, 0.15);
}

.service-item i {
    font-size: 2rem;
    color: #24cfa6;
    margin-bottom: 1rem;
    display: block;
}

.service-item h3 {
    font-size: 1.25rem;
    color: #2d3748;
    margin-bottom: 0.8rem;
    font-weight: 600;
}

.service-item p {
    color: #718096;
    font-size: 0.95rem;
    line-height: 1.5;
    flex-grow: 1;
}

.cta-section {
    text-align: center;
    background: linear-gradient(135deg, #24cfa6, #428e7c);
    border-radius: 15px;
    padding: 2.5rem;
    color: white;
}

.cta-section h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: white;
    color: #24cfa6;
}

.btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-secondary {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-secondary:hover {
    background: white;
    color: #24cfa6;
}

@media (max-width: 768px) {
    .hire-status-container {
        padding: 1rem;
    }
    
    .status-content {
        padding: 2rem;
    }
    
    .main-title {
        font-size: 2.2rem;
    }
    
    .contact-cards {
        grid-template-columns: 1fr;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}
