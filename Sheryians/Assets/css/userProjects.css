@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-medium_WrymeW95W.ttf?updatedAt=1713302126248") format("truetype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-extralight__KHggd4aZ.ttf?updatedAt=1713302127923") format("truetype");
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-regularit_AeweXTWiS.ttf?updatedAt=1713302129061") format("truetype");
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-semibold_AnJ7g--aJ.ttf?updatedAt=1713302125874") format("truetype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-black_qsRZ2yEB9.ttf?updatedAt=1713302122342") format("truetype");
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-mediumit_kwYDtLOMs.ttf?updatedAt=1713302125102") format("truetype");
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-bold_kpaGmQ4v_v.ttf?updatedAt=1713302125581") format("truetype");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-boldit_f6REbZK2g.ttf?updatedAt=1713302127856") format("truetype");
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-blackit_WeBJaLKA8-.ttf?updatedAt=1713302122300") format("truetype");
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-regular_mHnKLu9dd0.ttf?updatedAt=1713302122279") format("truetype");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-lightit_vzJK35d3aE.ttf?updatedAt=1713302122327") format("truetype");
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-extralightit_toKzXEsfr.ttf?updatedAt=1713302125832") format("truetype");
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-light_ivMEPQBZu.ttf?updatedAt=1713302125097") format("truetype");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-bold_prHhz92hiS.ttf?updatedAt=1713302122252") format("truetype");
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juan";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-thin_lHX4jBZFvC.ttf?updatedAt=1713302125648") format("truetype");
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juana-semiboldit_yfAC9hSzSh.ttf?updatedAt=1713302122272") format("truetype");
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-medium_L_cpau2EfA.ttf?updatedAt=1713302125668") format("truetype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-black_5k8c_h_Qxj.ttf?updatedAt=1713302122360") format("truetype");
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-thin_8GEkbrwkLn.ttf?updatedAt=1713302122425") format("truetype");
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-regular_33psZw8SQ.ttf?updatedAt=1713302122284") format("truetype");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-extralight_fokPbOsQ9.ttf?updatedAt=1713302125081") format("truetype");
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Juana";
    src: url("https://ik.imagekit.io/sheryians/Fonts/Fontspring-DEMO-juana-black,Fontspring-DEMO-juana-blackit,Fontspring-DEMO-juana-bold/Fontspring-DEMO-juanaalt-light_vjpuCX47rI.ttf?updatedAt=1713302125611") format("truetype");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}
/* 
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Regular_DQCmjH1st.ttf?updatedAt=1713347905052
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Ultrabold_jtJhX5ZopX.ttf?updatedAt=1713347905005
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Black_Jq-BOUQTx.ttf?updatedAt=1713347905045
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Ultralight_L18yrR2IlK.ttf?updatedAt=1713347905074
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Light_gwWCZxVuQl.ttf?updatedAt=1713347904991
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Medium_d0jNuroW9.ttf?updatedAt=1713347904973
https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Bold_WaMOsLX9Z9.ttf?updatedAt=1713347904936

*/
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Regular_DQCmjH1st.ttf?updatedAt=1713347905052") format("truetype");
    font-weight: 400;
    font-style: normal;
}
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Ultrabold_jtJhX5ZopX.ttf?updatedAt=1713347905005") format("truetype");
    font-weight: 800;
    font-style: normal;
}
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Black_Jq-BOUQTx.ttf?updatedAt=1713347905045") format("truetype");
    font-weight: 900;
    font-style: normal;
}
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Ultralight_L18yrR2IlK.ttf?updatedAt=1713347905074") format("truetype");
    font-weight: 200;
    font-style: normal;
}
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Light_gwWCZxVuQl.ttf?updatedAt=1713347904991") format("truetype");
    font-weight: 300;
    font-style: normal;
}
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Medium_d0jNuroW9.ttf?updatedAt=1713347904973") format("truetype");
    font-weight: 500;
    font-style: normal;
}
@font-face {
    font-family: "NeueMachina";
    src: url("https://ik.imagekit.io/sheryians/Fonts/neueMachina/NeueMachina-Bold_WaMOsLX9Z9.ttf?updatedAt=1713347904936") format("truetype");
    font-weight: 700;
    font-style: normal;
}
@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-Extrabold_YC6oQDa3Ix.ttf?updatedAt=1697720439904");
    font-family: "Gilroy";
    font-weight: 700;
}
@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-Bold_JK4cTGJOE.ttf?updatedAt=1697720175118");
    font-family: "Gilroy";
    font-weight: 600;
}
@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-Semibold_zTZ2PgJOF.ttf?updatedAt=1697720387357");
    font-family: "Gilroy";
    font-weight: 500;
}
@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-Medium_eHDK2RllDd.ttf?updatedAt=1697720216741");
    font-family: "Gilroy";
    font-weight: 400;
}
@font-face {
    src: url("https://ik.imagekit.io/sheryians/SiteStaticFiles/Gilroy-UltraLight_GObAYY-eiQ.ttf?updatedAt=1710171746221");
    font-family: "Gilroy";
    font-weight: 100;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Regular_5Mzhp8KlA8_CAVTbnsPOM.woff2?updatedAt=1714048137030");
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Thin_J0zaDW5kUV_cvf1ZCDypY.woff2?updatedAt=1714048137584");
    font-weight: 100;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Light_M4nzWhve6k_k7HolyzL10.woff2?updatedAt=1714048137233");
    font-weight: 300;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Medium_2wEhlmo-TD_wsqqEEwmJC.woff2?updatedAt=1714048136783");
    font-weight: 500;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Bold_VMHV5twUMR_2SBNfxj1ry.woff2?updatedAt=1714048134442");
    font-weight: bold;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Hairline_9Jz6JFa3S_u32EafJPG.woff2?updatedAt=1714048136620");
    font-weight: 100;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-LightIta_mLZfHYSDzL_zoX1nh7QCH.woff2?updatedAt=1714048137165");
    font-weight: 300;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-RegIta_ZQZ0ISuECo_Ebf0ebpNZw.woff2?updatedAt=1714048136741");
    font-weight: normal;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ThinIta__6kihetaOz_w4-Sx_LrF0.woff2?updatedAt=1714048137583");
    font-weight: 100;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-MedIta_s4wTiA5al_YBtT5S80dg.woff2?updatedAt=1714048137131");
    font-weight: 500;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-HairlineI_KIzj8Plwv_0Olitr0Zj1.woff2?updatedAt=1714048136797");
    font-weight: 100;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ExtBlkIta_f0giU-O0Yd_sAomO0ePGQ.woff2?updatedAt=1714048134074");
    font-weight: 900;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-BoldIta_O5gX7g1L5_EGb7W3WC7O.woff2?updatedAt=1714048134080");
    font-weight: bold;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-Black_-pjI4fOp4E_mmKDOiFYb1.woff2?updatedAt=1714048134434");
    font-weight: 900;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-BlackIta_y1WeOftIb_moP1jEfkvM.woff2?updatedAt=1714048134096");
    font-weight: 900;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ExtBlk_yunH-AhU2b_d1mB8gCvOj.woff2?updatedAt=1714048133983");
    font-weight: 900;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ExtLtIta_GJzq7onfxt_6WJ9e-RWZL.woff2?updatedAt=1714048134137");
    font-weight: 200;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ExtBdIta__MPkMs25CD_MEFsGC4G_T.woff2?updatedAt=1714048134073");
    font-weight: 800;
    font-style: italic;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ExtraBold_Qqt_sGOvuc_Z0ZOJEQucI.woff2?updatedAt=1714048134441");
    font-weight: 800;
    font-style: normal;
}
@font-face {
    font-family: "Helvetica";
    src: url("https://ik.imagekit.io/sheryians/Fonts/helvetica/HelveticaNowDisplay-ExtLt_R50yORjQ9_zDWWhh4hzv.woff2?updatedAt=1714048134072 ");
    font-weight: 200;
    font-style: normal;
}
@font-face {
    src: url("https://ik.imagekit.io/sheryians/Fonts/SourceCodePro-VariableFont_wght_6jNfJu5_qA.ttf?updatedAt=1731422147701");
    font-family: "SourceCodePro";
    font-weight: 400;
}
* {
    margin: 0%;
    padding: 0%;
    box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    text-rendering: optimizeSpeed;
    font-family: "Helvetica";
    color: var(--text-color);
}
*::-moz-selection {
    color: var(--ui-element-color) !important;
    background: var(--text-color) !important;
}
*::selection {
    color: var(--ui-element-color) !important;
    background: var(--text-color) !important;
}

*:focus {
    outline: none;
}

*::-moz-selection {
    color: var(--primaryLight);
    background: var(--primaryDark);
}

*::selection {
    color: var(--primaryLight);
    background: var(--primaryDark);
}

html,
body {
    height: 100%;
    width: 100%;
}

html ::-webkit-scrollbar,
body ::-webkit-scrollbar {
    width: 5px;
}

html ::-webkit-scrollbar-thumb,
body ::-webkit-scrollbar-thumb {
    border-radius: 3px;
}

body {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--cod-gray);
}
body main {
    max-width: 125rem;
}
body main .code-font {
    font-family: "SourceCodePro";
}
body #background {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    -o-object-fit: cover;
    object-fit: cover;
}

html {
    /* primary-colors */
    --cod-gray: #0c0c0c;
    --mercury: #e9e9e9;
    /* secondary-colors */
    --black: #000000;
    --gray-950: #121212;
    --Shark: #1e1e1e;
    --fuscous-gray: #4f4f4f;
    --boulder: #7c7c7c;
    --cod-gray-200: #d1d1d1;
    --gray-86: #dbdbdb;
    --Silver-Chalice: #b3b3b3;
    --white: #ffffff;
    --Wild-Sand: #f4f4f4;
    --background-color: #0c0c0c;
    --ternary-gray-color: #202020;
    /* Accent-colors */
    --caribbean-green: #24cfa6;
    --Lochinvar: #428e7c;
    /* warnings and error */
    --error-color: #882727;
    /* padding and sizing */
    --main-horizontal-padding: 4.9rem;
    /* variable in use */
    --text-color: var(--mercury);
    --ui-element-color: #2c2c2c;
    --ui-element-secondary-color: #171717;
    --ui-element-ternary-color: #1d1d1d;
    --ui-element-highlight-color: var(--gray-950);
    --accent-color: var(--caribbean-green);
    --sub-text-color: #9e9e9e;
    --form-input-border: #3c3c3c;
    --link-color: #0084ff;
    --discord-color: #7b40ff;
    --dot-dimension: 0.5em;
    font-size: clamp(14px, 0.85vw, 16px);
    --main-horizontal-padding: 5rem;
    transition: all 0.15s ease-in-out;
}

@media screen and (max-width: 600px) {
    html {
        --main-horizontal-padding: 4vw;
        font-size: clamp(12px, 1.5vw, 16px) !important;
    }
}
@media (max-width: 600px) {
    html ::-webkit-scrollbar,
    body ::-webkit-scrollbar {
        display: none;
    }
}
.popup {
    height: 100dvh;
    width: 100vw;
    position: fixed;
    top: 0;
    background-color: rgba(46, 46, 46, 0.167);
    -webkit-backdrop-filter: blur(1px);
    backdrop-filter: blur(1px);
    z-index: 2147483647;
    display: none;
    opacity: 0;
    align-items: center;
    justify-content: center;
    transition: all 0.3s linear;
    isolation: isolate;
}
.popup .bg {
    position: absolute;
    z-index: -1;
    height: 100%;
    width: 100%;
}
.popup.request-callback {
    font-size: 1.2em;
}
.popup.request-callback .center {
    padding: 2em 1.5em;
}
.popup.request-callback .center .text {
    gap: 0.7em;
    padding: 0;
}
.popup.request-callback .center .text form {
    width: 100%;
}
.popup.request-callback .center .closePopup {
    position: absolute;
    transform: translateX(-50%);
    right: 0.5em;
    top: 0.5em;
    cursor: pointer;
}
.popup .center {
    background-color: var(--ui-element-color);
    padding: 2rem;
    border-radius: 0.25rem;
    max-width: min(23rem, 85vw);
    text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 1rem;
    position: relative;
    z-index: 99999;
}
.popup .center #buttons {
    display: flex;
    gap: 20px;
}
.popup .center form {
    display: flex;
    flex-direction: column;
}
.popup .center form .form-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 0.7em;
    width: 100%;
}
.popup .center form .form-group .iti {
    width: 100%;
}
.popup .center form .form-group .iti input {
    padding-left: 3rem;
}
.popup .center form .form-group .iti .iti__country-list {
    background-color: var(--ui-element-color);
    color: white;
}
.popup .center form .form-group input {
    width: 100%;
    margin-top: 0.5em;
    margin-bottom: 0.2em;
    padding: 0.5em;
    border: none;
    border-radius: 0.25rem;
    background-color: var(--ui-element-secondary-color);
    color: var(--text-color);
    font-size: 1rem;
    font-family: "NeueMachina";
    transition: all 0.3s linear;
    color-scheme: dark;
}
.popup .center form .form-group input:focus {
    outline: none;
}
.popup .center form .form-group select {
    width: 100%;
    margin-top: 0.5em;
    margin-bottom: 0.2em;
    padding: 0.5em;
    border: none;
    border-radius: 0.25rem;
    background-color: var(--ui-element-secondary-color);
    color: var(--text-color);
    font-size: 1rem;
    font-family: "NeueMachina";
    transition: all 0.3s linear;
    color-scheme: dark;
}
.popup .center form .form-group select:focus {
    outline: none;
}
.popup .center form .form-group .error {
    display: none;
}
.popup .center form .form-group:has(input.error) .error {
    display: block;
}
.popup .center form .form-group:has(input.error) span.error {
    color: var(--error-color);
    font-size: 0.9em;
}
.popup .center form .submit {
    margin-top: 3em;
}
.popup .center .images {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}
.popup .center .images i {
    font-size: 2rem;
}
.popup .center .text {
    padding: 0.85rem;
    border-radius: 0.45rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}
.popup .center .text .buttons {
    width: 100%;
    justify-content: space-between;
    display: flex;
}
.popup .center .text .buttons .authorize {
    background-color: var(--caribbean-green);
    color: var(--white);
}
.popup .center .text .buttons .cancel {
    background-color: var(--boulder);
    color: var(--primaryDark);
}
.popup .center img {
    max-height: 3.5rem;
    -o-object-fit: contain;
    object-fit: contain;
}
.popup .center button {
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    border: none;
    padding: 0.2rem 0.5rem;
}
.popup .center p {
    opacity: 0.8;
}
.popup .center i {
    font-size: 1.1rem;
}
.popup .center textarea {
    border: none;
    resize: none;
    background-color: var(--primaryDark);
    border-radius: 10px;
    padding: 10px;
}
.popup .center .actions {
    gap: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}
.popup .center .actions .copy {
    height: 2.6rem;
    width: 2.6rem;
    border-radius: 50%;
    background-color: var(--ternary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.popup .center .actions .copy:hover {
    transition: all 0.3s linear;
    background-color: var(--secondaryLight);
}
.popup .center .actions img {
    height: 3rem;
    width: 3rem;
    -o-object-fit: cover;
    object-fit: cover;
}
.popup .center .closePopup {
    position: absolute;
    transform: translateX(-50%);
    left: 95%;
    top: 3%;
    cursor: pointer;
}

nav {
    position: fixed;
    top: 0;
    width: 100%;
    isolation: isolate;
    z-index: 111;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;
}
nav .phoneNumberPopup {
    position: absolute;
    width: 100vw;
    top: 0;
    left: 0;
    z-index: 991;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.79);
    display: flex;
    justify-content: center;
    align-items: center;
}
nav .phoneNumberPopup .forms {
    padding: 2rem 1.9rem;
    width: 30rem;
    overflow-x: scroll;
    background-color: var(--gray-950);
    border-radius: 7px;
    display: flex;
    gap: 2rem;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
}
nav .phoneNumberPopup .forms::-webkit-scrollbar {
    display: none;
}
nav .phoneNumberPopup .forms .details {
    position: relative;
    scroll-snap-align: center;
    display: flex;
    justify-content: center;
    flex-direction: column;
    flex-shrink: 0;
    width: 100%;
    transition: all 10s cubic-bezier(0.23, 1, 0.32, 1);
    gap: 0.5em;
}
nav .phoneNumberPopup .forms .details h2 {
    font-size: 1.6rem;
    font-weight: 400;
    text-align: center;
    letter-spacing: 0.05rem;
}
nav .phoneNumberPopup .forms .details .inputfield {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
}
nav .phoneNumberPopup .forms .details .inputfield h1 {
    font-size: 2rem;
}
nav .phoneNumberPopup .forms .details .inputfield p {
    color: grey;
    text-align: center;
}
nav .phoneNumberPopup .forms .details .inputfield .container {
    margin-top: 1.5rem;
    width: 100%;
    position: relative;
}
nav .phoneNumberPopup .forms .details .inputfield:has(> i) {
    width: -moz-fit-content;
    width: fit-content;
}
nav .phoneNumberPopup .forms .details .inputfield small {
    margin-top: 0.2rem;
    min-height: 0.7rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--error-color);
}
nav .phoneNumberPopup .forms .details .inputfield .inputWrapper {
    width: 100%;
    position: relative;
}
nav .phoneNumberPopup .forms .details .inputfield .inputWrapper:has(.showPassword)::before {
    visibility: hidden;
}
nav .phoneNumberPopup .forms .details .inputfield .inputWrapper::before,
nav .phoneNumberPopup .forms .details .inputfield .showPassword {
    content: "\eca0";
    font-family: "remixicon" !important;
    font-style: normal;
    position: absolute;
    top: 50%;
    right: 2%;
    transform: translate(-50%, -50%);
    color: var(--error-color);
    opacity: 0;
    z-index: 98;
}
nav .phoneNumberPopup .forms .details .inputfield .showPassword {
    content: "";
    color: var(--primaryDark);
    opacity: 1;
}
nav .phoneNumberPopup .forms .details .inputfield:has(.invalid) input {
    border: 1px solid var(--error-color) !important;
    background-color: rgba(231, 77, 60, 0.1058823529) !important;
}
nav .phoneNumberPopup .forms .details .inputfield:has(.invalid) .inputWrapper::before {
    opacity: 1;
}
nav .phoneNumberPopup .forms .details .inputfield label {
    text-transform: capitalize;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--sub-text-color);
}
nav .phoneNumberPopup .forms .details .inputfield .otp-container {
    position: relative;
    display: flex;
    gap: 0.5rem;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0.5rem;
}
nav .phoneNumberPopup .forms .details .inputfield .otp-container input {
    width: 3rem;
    padding: 0.5rem 0.8rem;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 500;
    border-radius: 2px;
    border: 1px solid var(--Shark);
    background-color: var(--Shark);
}
nav .phoneNumberPopup .forms .details .inputfield #otpErrorMessage {
    color: var(--error-color);
    font-size: 0.85rem;
    font-weight: 400;
    text-align: center;
}
nav .phoneNumberPopup .forms .details .inputfield #otpErrorMessage i {
    color: var(--error-color);
    font-size: 0.85rem;
    font-weight: 400;
}
nav .phoneNumberPopup .forms .details .inputfield #resend-otp {
    color: var(--primaryDark);
    font-size: 0.85rem;
    font-weight: 400;
    cursor: not-allowed;
    text-align: center;
}
nav .phoneNumberPopup .forms .details .inputfield .timer {
    color: var(--accent-color);
    font-size: 0.85rem;
    font-weight: 400;
    text-align: center;
}
nav .phoneNumberPopup .forms .details .inputfield #phone {
    background-color: var(--Shark);
    border: none;
    outline: none;
    padding-left: 3.2rem;
    padding-right: 2rem;
    border-radius: 2px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    border: 1px solid rgba(0, 0, 0, 0);
    position: relative;
    width: 100%;
}
nav .phoneNumberPopup .forms .details .inputfield input {
    background-color: var(--Shark);
    border: none;
    outline: none;
    padding: 0.4rem 0.8rem;
    padding-right: 2rem;
    border-radius: 2px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    border: 1px solid rgba(0, 0, 0, 0);
    position: relative;
    width: 100%;
    font-weight: 400;
}
nav .phoneNumberPopup .forms .details .inputfield input::-moz-placeholder {
    opacity: 0.6;
    font-weight: 400;
    font-size: 0.85rem;
    color: var(--fuscous-gray);
}
nav .phoneNumberPopup .forms .details .inputfield input::placeholder {
    opacity: 0.6;
    font-weight: 400;
    font-size: 0.85rem;
    color: var(--fuscous-gray);
}
nav .phoneNumberPopup .forms .details .inputfield i {
    cursor: pointer;
}
nav .phoneNumberPopup .forms .details .inputfield i:active {
    animation: clickButton 1.7s cubic-bezier(0.23, 1, 0.32, 1) 1 alternate;
}
nav .phoneNumberPopup .forms .details .checkbox {
    flex-direction: row-reverse;
    justify-content: flex-end;
}
nav .phoneNumberPopup .forms .details .checkbox label {
    opacity: 0.9;
}
nav .phoneNumberPopup .forms .details .celebrate {
    height: 90%;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}
nav .phoneNumberPopup .forms .details .celebrate .gif {
    content: "";
    height: 100%;
    width: 100%;
    background-image: url(/images/independence-day-fireworks-915-unscreen.gif);
    background-size: contain;
    background-repeat: round;
    background-position: center;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    filter: grayscale(1);
    visibility: hidden;
}
nav .phoneNumberPopup .forms .details .celebrate h1 {
    font-size: 1.1rem;
    text-align: center;
    font-weight: 200;
    color: var(--sub-text-color);
}
nav .phoneNumberPopup .forms .details .submitButton {
    width: 100%;
    display: flex;
    align-items: center;
    text-decoration: none;
    justify-content: center;
    background-color: var(--accent-color);
    border: none;
    padding: 0.7rem 0;
    height: 2rem;
    font-size: 0.8rem;
    border-radius: 2px;
    cursor: pointer;
    transition: all 1.3s cubic-bezier(0.23, 1, 0.32, 1);
}
@keyframes clickButton {
    0% {
        scale: 0.97;
    }
    100% {
        scale: 1;
    }
}
nav .phoneNumberPopup .forms .details .submitButton:active {
    animation: clickButton 1.7s cubic-bezier(0.23, 1, 0.32, 1) 1 alternate;
}
nav .phoneNumberPopup .forms .details .submitButton i {
    margin-left: 0.6rem;
}
nav .phoneNumberPopup .forms .details .submitButton img {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    transform: scale(5);
}
nav .phoneNumberPopup .forms #finish .submitButton {
    visibility: hidden;
}
nav .header {
    width: 100%;
    margin: auto;
    padding: 0.5rem var(--main-horizontal-padding);
    padding-top: 1rem;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    position: relative;
    display: flex;
    justify-content: space-between;
    max-width: 125rem;
}
nav .header * {
    font-family: "NeueMachina";
}
nav .header .left {
    display: flex;
    align-items: center;
    max-width: 38%;
    cursor: pointer;
}
nav .header .left span {
    font-size: 1.1em;
    line-height: 1.3;
}
nav .header .left img {
    width: 1.5em;
    margin-top: -0.2em;
}
nav .header .right {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}
nav .header .right .nav-item {
    --nav-item-horizontal-padding: 1em;
    border: none;
    outline: none;
    font-size: 1rem;
    padding: 0.5em var(--nav-item-horizontal-padding);
    cursor: pointer;
    background-color: transparent;
    font-weight: 300;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    text-decoration: none;
}
nav .header .right .nav-item:has(a.btn) {
    padding: 0;
}
nav .header .right .nav-item.menu {
    display: none;
    z-index: 101;
}
nav .header .right .nav-item.menu .menu-toggle {
    font-size: 1.5rem;
    width: 1em;
    height: 1em;
    padding: 0 !important;
    z-index: 3;
    position: relative !important;
}
nav .header .right .nav-item.menu .menu-toggle .line {
    height: 0.1em;
    width: 1em;
    background-color: var(--text-color);
    position: absolute;
    --gap: 0.25em;
    --animation-duration: 0.2s;
    --animation-interval-time: 0.05s;
}
nav .header .right .nav-item.menu .menu-toggle .line.line1 {
    top: calc(50% - var(--gap));
    left: 0;
    top: calc(50% - var(--gap));
}
nav .header .right .nav-item.menu .menu-toggle .line.line2 {
    bottom: calc(50% - var(--gap));
    right: 0;
    bottom: calc(50% - var(--gap));
}
nav .header .right .nav-item.menu .menu-toggle.menu-show .line {
    transform: translate(-50%, -50%);
}
nav .header .right .nav-item.menu .menu-toggle.menu-show .line.line1 {
    animation: vanished-from-right var(--animation-duration) linear both, from-top-left-to-bottom-right var(--animation-duration) linear calc(var(--animation-duration) + var(--animation-interval-time)) both;
}
nav .header .right .nav-item.menu .menu-toggle.menu-show .line.line2 {
    animation: vanished-from-left var(--animation-duration) linear both, from-top-right-to-bottom-left var(--animation-duration) linear calc(var(--animation-duration) + var(--animation-interval-time)) both;
}
@keyframes vanished-from-right {
    from {
        transform-origin: right;
        transform: translate(0, 0%);
        left: 0;
        width: 1em;
    }
    to {
        width: 0;
        transform: translate(0, 0%);
        left: 0;
    }
}
@keyframes vanished-from-left {
    from {
        right: 0%;
        left: initial;
        transform: translate(0, 0%);
        width: 1em;
    }
    to {
        right: 0%;
        left: initial;
        width: 0;
        transform: translate(0, 0%);
    }
}
@keyframes from-top-left-to-bottom-right {
    1% {
        top: 0%;
        left: 0%;
        width: 0;
        transform: translate(0%, 0%) rotate(45deg);
        transform-origin: left;
    }
    100% {
        top: 0%;
        left: 0%;
        transform-origin: left;
        width: 141.421356237%;
        transform: translate(0%, 0%) rotate(45deg);
    }
}
@keyframes from-top-right-to-bottom-left {
    1% {
        width: 0%;
        top: 0%;
        right: 0%;
        transform: translate(0, 0) rotate(-45deg);
        transform-origin: right;
    }
    100% {
        top: 0%;
        right: 0%;
        width: 141.421356237%;
        transform: translate(0, 0) rotate(-45deg);
        transform-origin: right;
    }
}
nav .header .right .nav-item.menu .menu-toggle.menu-close .line1 {
    animation: from-bottom-right-to-bottom-center var(--animation-duration) linear both;
}
nav .header .right .nav-item.menu .menu-toggle.menu-close .line2 {
    animation: from-bottom-left-to-top-center var(--animation-duration) linear both;
}
@keyframes from-bottom-left-to-top-center {
    1% {
        top: 0%;
        right: 0%;
        width: 141.421356237%;
        transform: translate(0, 0) rotate(-45deg);
        transform-origin: right;
    }
    50% {
        top: 50%;
        bottom: 50%;
        width: 1em;
        transform: translate(0%, 0%) rotate(0deg);
    }
    100% {
        bottom: calc(50% - var(--gap));
        top: initial;
        right: 0%;
        width: 1em;
        transform: translate(0, 0) rotate(0deg);
        transform-origin: right;
    }
}
@keyframes from-bottom-right-to-bottom-center {
    1% {
        top: 0%;
        left: 0%;
        width: 141.421356237%;
        transform: translate(0%, 0%) rotate(45deg);
        transform-origin: left;
    }
    50% {
        top: 50%;
        bottom: 50%;
        width: 1em;
        transform: translate(0%, 0%) rotate(0deg);
    }
    100% {
        bottom: initial;
        top: calc(50% - var(--gap));
        left: 0%;
        transform-origin: left;
        width: 1em;
        transform: translate(0%, 0%) rotate(0deg);
    }
}
nav .header .right .nav-item.menu:has(.menu-show) .menu-panel {
    transform: translateX(0);
}
nav .header .right .nav-item.menu .menu-panel {
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    background-color: var(--background-color);
    transform: translateX(100%);
    transition: all 0.4s ease-in-out;
}
nav .header .right .nav-item.menu .menu-panel * {
    text-align: left;
}
nav .header .right .nav-item.menu .menu-panel .menu-header {
    font-size: 2em;
    padding: 1.3rem 7vw;
    padding-top: 2.7rem;
    border-bottom: 1px solid var(--fuscous-gray);
}
nav .header .right .nav-item.menu .menu-panel .menu-header h1 {
    font-size: 1.3em;
    font-weight: 300;
}
nav .header .right .nav-item.menu .menu-panel .menu-items {
    padding: 0.5rem 0;
    padding-top: 4rem;
}
nav .header .right .nav-item.menu .menu-panel .menu-items .menu-item {
    font-size: 2em;
    color: var(--boulder);
}
nav .header .right .nav-item a {
    text-decoration: none;
}
nav .header .right .nav-item a.btn {
    font-size: 1.1em;
    background-color: var(--accent-color);
    font-weight: 500;
    font-family: "Helvetica";
    padding: 0.5em 1.5em;
    border-radius: 0.2em;
}
nav .header .right .nav-item.auth-user .text-profile-initial {
    aspect-ratio: 1/1;
    width: 2em;
    padding: 0;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0;
    overflow: hidden;
    background-color: transparent;
}
nav .header .right .nav-item.auth-user .text-profile-initial img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
nav .header .right .nav-item.request-callback {
    display: flex;
    align-items: center;
    gap: 0.5em;
}
nav .header .right .nav-item.request-callback i {
    font-size: 1.5em;
    display: none;
}
nav .header .right .nav-item.request-callback span {
    display: initial;
    white-space: nowrap;
}
nav .header .nav-item:has(.notification-panel) {
    isolation: isolate;
    position: relative;
    z-index: 100;
}
nav .header .nav-item .notification-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
}
nav .header .nav-item .notification-toggle i {
    display: none;
    font-size: 1.7em;
}
nav .header .nav-item .notification-toggle svg {
    height: 1.7em;
}
nav .header .nav-item:has(.show) .notification-toggle i {
    display: block;
}
nav .header .nav-item:has(.show) .notification-toggle svg {
    display: none;
}
nav .header .nav-item:has(.show) .remove-notification-panel {
    display: flex;
    cursor: default;
}
nav .header .notification-icon:has(.new) .notification-toggle {
    position: relative;
}
nav .header .notification-icon:has(.new) .notification-toggle::before {
    content: "";
    position: absolute;
    height: var(--dot-dimension);
    aspect-ratio: 1/1;
    border-radius: 50%;
    background-color: var(--error-color);
    top: -0.2em;
    right: -0.2em;
}
nav .header .notification-icon:has(.new):has(.show) .notification-toggle::before {
    display: none;
}
nav .header .notification-panel {
    display: grid;
    grid-template-rows: 0fr;
    transition: all 0.3s linear;
    font-size: 1rem;
    top: 3rem;
    right: 2rem;
    position: absolute;
}
nav .header .notification-panel.show {
    grid-template-rows: 1fr;
}
nav .header .notification-panel .notification-content {
    width: 28em;
    max-width: 90vw;
    max-height: min(60vh, 30em);
    border-radius: 0.5em;
    background-color: var(--gray-950);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    cursor: default;
}
nav .header .notification-panel .notification-content *::-webkit-scrollbar {
    display: none;
}
nav .header .notification-panel .notification-content > * {
    padding: calc(var(--main-horizontal-padding) / 5);
    width: 100%;
}
nav .header .notification-panel .notification-content * {
    text-align: left;
}
nav .header .notification-panel .notification-content .heading {
    border-bottom: 1px solid var(--gray-86);
    position: sticky;
}
nav .header .notification-panel .notification-content .notifications {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 !important;
    overflow-y: auto;
}
nav .header .notification-panel .notification-content .notifications * {
    font-family: "Helvetica";
}
nav .header .notification-panel .notification-content .notifications .notification {
    --padding-from-left: calc(var(--main-horizontal-padding) / 2.5);
    display: flex;
    gap: 1em;
    width: 100%;
    cursor: pointer;
    padding: calc(var(--main-horizontal-padding) / 5);
    padding-left: var(--padding-from-left);
    position: relative;
}
nav .header .notification-panel .notification-content .notifications .notification:hover {
    background-color: var(--form-input-border);
    transition: all 0.3s linear;
}
nav .header .notification-panel .notification-content .notifications .notification .left {
    width: 100%;
    max-width: initial;
    padding-top: 0.2em;
}
nav .header .notification-panel .notification-content .notifications .notification .left p,
nav .header .notification-panel .notification-content .notifications .notification .left small {
    line-height: 0.8;
}
nav .header .notification-panel .notification-content .notifications .notification .left small {
    color: var(--sub-text-color);
    opacity: 0.7;
}
nav .header .notification-panel .notification-content .notifications .notification .right {
    width: 100%;
    max-width: 7em;
    border-radius: 0.4em;
    overflow: hidden;
}
nav .header .notification-panel .notification-content .notifications .notification .right img {
    aspect-ratio: 16/9;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
nav .header .notification-panel .notification-content .notifications .notification.new::after {
    content: "";
    color: var(--Wild-Sand);
    font-size: 0.8em;
    font-weight: 500;
    position: absolute;
    left: calc(var(--padding-from-left) / 2);
    top: calc(var(--padding-from-left) / 1.5);
    height: var(--dot-dimension);
    aspect-ratio: 1/1;
    border-radius: 50%;
    background-color: var(--accent-color);
}
nav .header .remove-notification-panel {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: transparent;
    z-index: -1;
    transition: all 0.7s linear;
    -webkit-backdrop-filter: blur(0px);
    backdrop-filter: blur(0px);
}
nav .header .remove-notification-panel.show {
    display: block;
}
@media (max-width: 600px) {
    nav .header .left {
        max-width: 20ch;
    }
    nav .header .left img {
        width: 2em;
    }
    nav .header .right {
        gap: 0;
    }
    nav .header .right .nav-item {
        display: none;
        position: initial;
        padding: 0.5em 0.9em;
    }
    nav .header .right .nav-item.mobile-item {
        display: block;
    }
    nav .header .right .nav-item.request-callback i {
        display: block;
    }
    nav .header .right .nav-item.request-callback span {
        display: none;
    }
    nav .header .right .nav-item.menu {
        display: flex;
    }
    nav .header .notification-panel {
        top: 6rem;
        font-size: 1.2rem;
    }
    nav .header .notification-panel .heading {
        padding: calc(var(--main-horizontal-padding) / 2);
    }
    nav .header .notification-panel .notifications .notification {
        padding: calc(var(--main-horizontal-padding) / 1) !important;
        padding-top: calc(var(--main-horizontal-padding) / 2) !important;
        padding-bottom: calc(var(--main-horizontal-padding) / 2) !important;
    }
    nav .header .notification-panel .notifications .notification.new::after {
        top: calc(var(--main-horizontal-padding) / 1.5) !important;
    }
}

main {
    width: 100%;
}
main .view {
    width: 100%;
    padding: var(--main-horizontal-padding);
}

#phonePopup {
    top: 0;
    display: none;
    position: fixed;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.616);
    z-index: 999;
    justify-content: center;
    align-items: center;
}
#phonePopup #popupElements {
    position: relative;
    padding: 10px;
    height: 45vh;
    width: 30vw;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;
    background-color: var(--ui-element-color);
    min-width: 25rem;
    min-height: 23rem;
    align-items: center;
}
#phonePopup #popupElements .logo {
    width: 6rem;
    height: 6rem;
    overflow: hidden;
}
#phonePopup #popupElements .logo img {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
#phonePopup #popupElements .iti {
    width: 15em;
    display: flex;
    align-items: center;
    justify-content: center;
}
#phonePopup #popupElements .iti .iti--allow-dropdown {
    width: 100%;
}
#phonePopup #popupElements .iti .iti--allow-dropdown input {
    border: 2px solid transparent;
    width: 100%;
    padding-left: 3.5rem;
}
#phonePopup #popupElements p {
    text-align: center;
    padding: 0 1rem;
    min-width: 10rem;
    font-weight: 600;
    opacity: 0.9;
}
#phonePopup #popupElements #popupClose {
    transform: scale(1.3);
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    color: var(--text-color);
    cursor: pointer;
}
#phonePopup #popupElements input {
    padding: 7px 20px;
    border: none;
    font-size: 1rem;
    outline: none;
    outline: none;
    width: 40%;
    color: var(--background-color);
    border-radius: 0.2rem;
    transition: 0.2s;
}
#phonePopup #popupElements .iti__country-list .iti__country-name {
    color: var(--background-color);
}
#phonePopup #popupElements #emailInput {
    width: 70% !important;
}
#phonePopup #popupElements button {
    padding: 10px 20px;
    border-radius: 5px;
    outline: none;
    background-color: var(--primary);
    color: var(--text-color);
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
}
#phonePopup #popupElements .numberError,
#phonePopup #popupElements .emailError {
    margin-top: -10px;
    color: red;
    opacity: 0;
    transition: 0.2s;
    font-size: 0.7rem;
}

#footer {
    padding: var(--main-horizontal-padding);
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 2rem;
}
#footer .top {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: space-between;
    padding-top: 7rem;
    padding-bottom: 7rem;
    position: relative;
}
#footer .top::after,
#footer .top::before {
    content: "";
    position: absolute;
    top: 0;
    transform: translateX(-50%);
    left: 50%;
    width: 105%;
    height: 0.05rem;
    background-color: var(--boulder);
    opacity: 0.5;
}
#footer .top::before {
    top: initial;
    bottom: 0;
}
#footer .top .footer-section {
    display: flex;
    gap: 1.3rem;
    flex-direction: column;
}
#footer .top .footer-section > a {
    font-size: 1.3rem;
}
#footer .top .footer-section:nth-child(1) {
    flex-basis: 18rem;
    margin-top: 1rem;
}
#footer .top .footer-section .logo {
    width: 3rem;
}
#footer .top .footer-section .socials {
    display: flex;
    gap: 2rem;
}
#footer .top .footer-section .socials a {
    text-decoration: none;
}
#footer .top .footer-section .socials i {
    font-size: 1.7rem;
}
#footer .top .footer-section h1 {
    font-size: 1.1rem;
}
#footer .top .footer-section .options {
    display: flex;
    flex-direction: column;
    gap: 1.05rem;
    margin-top: 0.4rem;
}
#footer .top .footer-section .options a {
    font-weight: 300;
    font-size: 1.05rem;
    text-decoration: none;
    transition: cubic-bezier(0.19, 1, 0.22, 1) 0.5s;
}
#footer .top .footer-section .options a:hover {
    color: var(--accent-color);
}
#footer .top .footer-section:nth-last-child(1) a {
    max-width: 17ch;
}
#footer .bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 1.4rem;
}
#footer .bottom h1 {
    font-size: 1rem;
}
@media (max-width: 600px) {
    #footer {
        flex-wrap: nowrap;
    }
    #footer .top {
        flex-direction: column;
        gap: 2rem;
    }
    #footer .top .footer-section:nth-child(1) {
        flex-basis: initial;
        margin-bottom: 2rem;
    }
    #footer .top .footer-section .options {
        gap: 0.7rem;
    }
    #footer .top .footer-section .options a {
        font-size: 1.3rem;
    }
    #footer .top .footer-section:nth-last-child(1) a {
        max-width: 34ch;
    }
    #footer .bottom {
        padding-bottom: 4rem;
    }
}

.text-highlight {
    color: var(--accent-color);
    text-decoration: none;
}

.btn {
    padding: 0.45em 1.5em;
    width: -moz-fit-content;
    width: fit-content;
    color: var(--black);
    border-radius: 0.4em;
    border: none;
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
}
.btn:hover {
    filter: brightness(1.08);
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--black);
}

.btn-secondary {
    background-color: var(--link-color) !important;
    color: var(--mercury);
}

.btn-dark {
    background-color: var(--form-input-border);
    color: var(--mercury);
}

.btn-ternary {
    background-color: var(--sub-text-color);
    color: var(--ui-element-color);
}

.discord {
    background-color: var(--discord-color) !important;
}
@media (prefers-color-scheme: light) {
    .discord {
        color: var(--ui-element-color) !important;
    }
}

.color-white {
    color: var(--white) !important;
}

.shadow-bottom {
    box-shadow: 0px 10px 10px -5px rgba(236, 236, 236, 0.6);
}

.shadow-primary {
    box-shadow: 0px 0.7em 2em -1em var(--accent-color);
}

.loader-container {
    position: absolute;
    bottom: -2rem;
    left: 0;
    width: 100%;
}
.loader-container .loader {
    background-color: rgba(221, 221, 221, 0);
    border-radius: 4px;
    overflow: hidden;
    height: 1px;
}
.loader-container .progress {
    height: 100%;
    width: 0%;
    background: var(--accent-color);
    transition: width 0.1s ease-in-out;
}
.loader-container .percentage {
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
}

.user-container {
    margin: 0 auto;
    width: 90%;
    padding: 2rem;
    background: #0c0c0c;
    min-height: 100vh;
}
@media (max-width: 768px) {
    .user-container {
        padding: 1rem;
    }
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: #1e1e1e;
    border-radius: 12px;
    border: 1px solid #333333;
}
@media (max-width: 768px) {
    .user-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
}
.user-header .header-content {
    flex: 1;
}
.user-header .header-content h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}
@media (max-width: 768px) {
    .user-header .header-content h1 {
        font-size: 2rem;
    }
}
.user-header .header-content p {
    color: #b0b0b0;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0;
}
.user-header .header-actions .submit-new-btn {
    background: linear-gradient(135deg, #24cfa6, rgb(71, 223, 186.5555555556));
    color: #0c0c0c;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}
.user-header .header-actions .submit-new-btn .btn-icon {
    font-size: 1.2rem;
    font-weight: bold;
}
.user-header .header-actions .submit-new-btn:active {
    transform: translateY(0);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

.stat-card {
    background: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}
.stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #24cfa6;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}
.stat-card.total::before {
    background: #24cfa6;
}
.stat-card.pending::before {
    background: #f39c12;
}
.stat-card.approved::before {
    background: #24cfa6;
}
.stat-card.rejected::before {
    background: #e74c3c;
}
.stat-card .stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}
.stat-card .stat-content {
    flex: 1;
}
.stat-card .stat-content .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}
@media (max-width: 768px) {
    .stat-card .stat-content .stat-number {
        font-size: 1.5rem;
    }
}
.stat-card .stat-content .stat-label {
    color: #b0b0b0;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #1e1e1e;
    border-radius: 12px;
    border: 1px solid #333333;
    gap: 1rem;
}
@media (max-width: 768px) {
    .controls-section {
        flex-direction: column;
        align-items: stretch;
    }
}
.controls-section .filter-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}
.controls-section .filter-group label {
    color: #ffffff;
    font-weight: 500;
    white-space: nowrap;
}
.controls-section .filter-group .filter-dropdown {
    background: #0c0c0c;
    color: #ffffff;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: max(16px, 0.9rem);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}
.controls-section .search-group {
    position: relative;
    flex: 1;
    max-width: 300px;
}
.controls-section .search-group .search-input {
    width: 100%;
    background: #0c0c0c;
    color: #ffffff;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    font-size: max(16px, 0.9rem);
    transition: all 0.3s ease;
}
.controls-section .search-group .search-input::-moz-placeholder {
    color: #b0b0b0;
}
.controls-section .search-group .search-input::placeholder {
    color: #b0b0b0;
}
.controls-section .search-group .search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #b0b0b0;
    pointer-events: none;
}

.projects-table-container {
    background: #1e1e1e;
    border-radius: 12px;
    border: 1px solid #333333;
    overflow: hidden;
}

.projects-table .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #333333;
    background: rgba(36, 207, 166, 0.05);
}
.projects-table .table-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}
.projects-table .table-header .table-info {
    color: #b0b0b0;
    font-size: 0.9rem;
}
.projects-table .table-content {
    overflow-x: auto;
}
.projects-table .table-content table {
    width: 100%;
    border-collapse: collapse;
}
.projects-table .table-content table thead {
    background: rgba(12, 12, 12, 0.5);
}
.projects-table .table-content table thead th {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem 1.5rem;
    text-align: left;
    border-bottom: 1px solid #333333;
}
.projects-table .table-content table thead th:first-child {
    padding-left: 2rem;
}
.projects-table .table-content table thead th:last-child {
    padding-right: 2rem;
}
.projects-table .table-content table tbody .project-row {
    border-bottom: 1px solid #333333;
    transition: all 0.3s ease;
}
.projects-table .table-content table tbody .project-row.pending {
    border-left: 4px solid #f39c12;
}
.projects-table .table-content table tbody .project-row.approved {
    border-left: 4px solid #24cfa6;
}
.projects-table .table-content table tbody .project-row.rejected {
    border-left: 4px solid #e74c3c;
}
.projects-table .table-content table tbody .project-row td {
    padding: 1.5rem;
    vertical-align: top;
}
.projects-table .table-content table tbody .project-row td:first-child {
    padding-left: 2rem;
}
.projects-table .table-content table tbody .project-row td:last-child {
    padding-right: 2rem;
}

.project-details .project-title {
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
}
.project-details .project-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
.project-details .project-links .project-link {
    color: #24cfa6;
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
    transition: all 0.3s ease;
}
.project-details .project-links .project-link .link-icon {
    font-size: 0.8rem;
}
.project-details .project-links .project-link.github {
    color: #3498db;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.status-badge.pending {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.3);
}
.status-badge.approved {
    background: rgba(36, 207, 166, 0.1);
    color: #24cfa6;
    border: 1px solid rgba(36, 207, 166, 0.3);
}
.status-badge.rejected {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.date-info .date-text {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}
.date-info .time-text {
    color: #b0b0b0;
    font-size: 0.8rem;
}

.review-info .review-date {
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}
.review-info .review-notes-preview {
    color: #24cfa6;
    font-size: 0.8rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.3s ease;
}
.review-info .review-notes-preview .notes-icon {
    font-size: 0.7rem;
}
.review-info .no-notes {
    color: #b0b0b0;
    font-size: 0.8rem;
    font-style: italic;
}

.pending-review {
    color: #b0b0b0;
    font-size: 0.9rem;
    font-style: italic;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}
.action-buttons .action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    border: 1px solid #333333;
    background: #0c0c0c;
    color: #b0b0b0;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}
.action-buttons .action-btn .btn-icon {
    font-size: 0.9rem;
}

.project-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.project-modal.show {
    opacity: 1;
}
.project-modal .modal-content {
    background: #1e1e1e;
    border-radius: 12px;
    border: 1px solid #333333;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}
.project-modal .modal-content .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #333333;
}
.project-modal .modal-content .modal-header h3 {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}
.project-modal .modal-content .modal-header .modal-close {
    background: none;
    border: none;
    color: #b0b0b0;
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.3s ease;
}
.project-modal .modal-content .modal-body {
    padding: 2rem;
}
.project-modal .modal-content .modal-body .project-status {
    margin-bottom: 1.5rem;
}
.project-modal .modal-content .modal-body .project-info-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}
.project-modal .modal-content .modal-body .project-info-grid .info-item label {
    display: block;
    color: #b0b0b0;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}
.project-modal .modal-content .modal-body .project-info-grid .info-item span,
.project-modal .modal-content .modal-body .project-info-grid .info-item a {
    color: #ffffff;
    font-size: 0.9rem;
}
.project-modal .modal-content .modal-body .project-info-grid .info-item a {
    color: #24cfa6;
    text-decoration: none;
}
.project-modal .modal-content .modal-body .review-feedback h4 {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}
.project-modal .modal-content .modal-body .review-feedback .feedback-content {
    background: rgba(12, 12, 12, 0.5);
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #24cfa6;
    color: #b0b0b0;
    line-height: 1.5;
    font-style: italic;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem;
    color: #b0b0b0;
}
.loading-state .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(36, 207, 166, 0.1);
    border-top: 3px solid #24cfa6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}
.loading-state p {
    font-size: 1.1rem;
    margin: 0;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}
.empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
.empty-state h3 {
    color: #ffffff;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}
.empty-state p {
    color: #b0b0b0;
    font-size: 1rem;
    margin-bottom: 2rem;
    max-width: 400px;
}
.empty-state .submit-new-btn {
    background: linear-gradient(135deg, #24cfa6, rgb(71, 223, 186.5555555556));
    color: #0c0c0c;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}
.empty-state .submit-new-btn .btn-icon {
    font-size: 1.2rem;
    font-weight: bold;
}
.empty-state .submit-new-btn:active {
    transform: translateY(0);
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}
.notification.show {
    transform: translateX(0);
}
.notification.success {
    background: rgba(36, 207, 166, 0.1);
    color: #24cfa6;
    border: 1px solid rgba(36, 207, 166, 0.3);
}
.notification.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
@media (max-width: 1024px) {
    .projects-table .table-content table {
        font-size: 0.9rem;
    }
    .projects-table .table-content table th,
    .projects-table .table-content table td {
        padding: 1rem;
    }
}
@media (max-width: 768px) {
    .user-header {
        padding: 1.5rem;
    }
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    .stat-card .stat-icon {
        font-size: 1.5rem;
    }
    .stat-card .stat-content .stat-number {
        font-size: 1.5rem;
    }
    .controls-section {
        padding: 1rem;
    }
    .projects-table .table-content .project-details .project-links .project-link {
        font-size: 0.8rem;
    }
    .projects-table .table-content .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    .projects-table .table-content .action-buttons .action-btn {
        width: 32px;
        height: 32px;
    }
    .project-modal .modal-content {
        width: 95%;
        margin: 1rem;
    }
    .project-modal .modal-content .modal-body {
        padding: 1.5rem;
    }
} /*# sourceMappingURL=userProjects.css.map */
