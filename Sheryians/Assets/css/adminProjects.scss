// Admin Projects Page Styles
// Matches the Sheryians design system with black theme and accent colors

@import "common.scss";

html,
body {
    height: 100%;
    width: 100%;
    font-size: 16px !important;
}

body {
    background: var(--background-color);
    color: var(--text-color);
    font-family: "NeueMachina", "<PERSON>", sans-serif;
}

// Main container
.admin-container {
    width: 90%;
    min-height: 100vh;
    padding: 2rem var(--main-horizontal-padding);

    @media (max-width: 600px) {
        padding: 1rem;
    }
}

// Header section 
.admin-header {
    margin-bottom: 2.5rem;

    h1 {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-color);
        font-family: "NeueMachina", sans-serif;

        @media (max-width: 600px) {
            font-size: 2rem;
        }
    }

    p {
        color: var(--sub-text-color);
        font-size: 1rem;
        font-weight: 300;
    }
}

// Statistics cards
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;

    @media (max-width: 600px) {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.stat-card {
    background: var(--ui-element-color);
    border: 1px solid var(--ui-element-ternary-color);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        border-color: var(--accent-color);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--accent-color);
        font-family: "NeueMachina", sans-serif;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        color: var(--sub-text-color);
        text-transform: uppercase;
        letter-spacing: 0.05rem;
    }

    &.pending .stat-number {
        color: #ff9500;
    }

    &.approved .stat-number {
        color: var(--accent-color);
    }

    &.rejected .stat-number {
        color: var(--error-color);
    }
}

// Controls section
.controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    @media (max-width: 600px) {
        flex-direction: column;
        align-items: stretch;
    }
}

// Filter dropdown
.filter-dropdown {
    background: var(--ui-element-color);
    border: 1px solid var(--ui-element-ternary-color);
    color: var(--text-color);
    padding: 0.7rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;

    &:focus {
        outline: none;
        border-color: var(--accent-color);
        box-shadow: 0 0 0 2px rgba(36, 207, 166, 0.1);
    }

    &:hover {
        border-color: var(--accent-color);
    }

    option {
        background: var(--ui-element-color);
        color: var(--text-color);
    }
}

// Search input
.search-input {
    background: var(--ui-element-color);
    border: 1px solid var(--ui-element-ternary-color);
    color: var(--text-color);
    padding: 0.7rem 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    min-width: 250px;

    &::placeholder {
        color: var(--sub-text-color);
        opacity: 0.7;
    }

    &:focus {
        outline: none;
        border-color: var(--accent-color);
        box-shadow: 0 0 0 2px rgba(36, 207, 166, 0.1);
    }

    @media (max-width: 600px) {
        min-width: 100%;
    }
}

// Projects table
.projects-table {
    background: var(--ui-element-color);
    border: 1px solid var(--ui-element-ternary-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    table {
        width: 100%;
        border-collapse: collapse;

        thead {
            background: var(--ui-element-secondary-color);

            th {
                padding: 1rem;
                text-align: left;
                font-weight: 600;
                color: var(--text-color);
                font-size: 0.85rem;
                text-transform: uppercase;
                letter-spacing: 0.05rem;
                border-bottom: 1px solid var(--ui-element-ternary-color);

                @media (max-width: 600px) {
                    padding: 0.7rem 0.5rem;
                    font-size: 0.75rem;
                }
            }
        }

        tbody {
            tr {
                border-bottom: 1px solid var(--ui-element-ternary-color);
                transition: all 0.2s ease;

                &:hover {
                    background: var(--ui-element-ternary-color);
                }

                &:last-child {
                    border-bottom: none;
                }

                td {
                    padding: 1rem;
                    vertical-align: middle;

                    @media (max-width: 600px) {
                        padding: 0.7rem 0.5rem;
                        font-size: 0.85rem;
                    }
                }
            }
        }
    }
}

// Status badges
.status-badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05rem;

    &.pending {
        background: rgba(255, 149, 0, 0.15);
        color: #ff9500;
        border: 1px solid rgba(255, 149, 0, 0.3);
    }

    &.approved {
        background: rgba(36, 207, 166, 0.15);
        color: var(--accent-color);
        border: 1px solid rgba(36, 207, 166, 0.3);
    }

    &.rejected {
        background: rgba(136, 39, 39, 0.15);
        color: var(--error-color);
        border: 1px solid rgba(136, 39, 39, 0.3);
    }
}

// Action buttons
.action-btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 0.2rem;

    &.approve {
        background: rgba(36, 207, 166, 0.15);
        color: var(--accent-color);
        border: 1px solid rgba(36, 207, 166, 0.3);

        &:hover {
            background: var(--accent-color);
            color: var(--black);
        }
    }

    &.reject {
        background: rgba(136, 39, 39, 0.15);
        color: var(--error-color);
        border: 1px solid rgba(136, 39, 39, 0.3);

        &:hover {
            background: var(--error-color);
            color: var(--white);
        }
    }

    &.view {
        background: var(--ui-element-ternary-color);
        color: var(--text-color);
        border: 1px solid var(--form-input-border);

        &:hover {
            background: var(--form-input-border);
        }
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
            background: initial;
            color: initial;
        }
    }
}

// Links
.project-link {
    color: var(--link-color);
    text-decoration: none;
    font-weight: 500;

    &:hover {
        text-decoration: underline;
        color: var(--accent-color);
    }
}

// User info
.user-info {
    .user-name {
        font-weight: 500;
        color: var(--text-color);
    }

    .user-email {
        font-size: 0.8rem;
        color: var(--sub-text-color);
        margin-top: 0.2rem;
    }
}

// Date formatting
.date-text {
    font-size: 0.85rem;
    color: var(--sub-text-color);
}

// Loading state
.loading-state {
    text-align: center;
    padding: 3rem;
    color: var(--sub-text-color);

    .spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--ui-element-ternary-color);
        border-top: 3px solid var(--accent-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// Empty state
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--sub-text-color);

    .empty-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--fuscous-gray);
    }

    h3 {
        margin-bottom: 0.5rem;
        color: var(--text-color);
    }
}

// Mobile responsive table
@media (max-width: 768px) {
    .projects-table {
        overflow-x: auto;

        table {
            min-width: 600px;
        }
    }

    .controls-section {
        .filter-dropdown,
        .search-input {
            min-width: 100%;
        }
    }
}

// Notification styles (same as project upload)
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 1000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    min-width: 300px;

    &.show {
        opacity: 1;
        transform: translateX(0);
    }

    &.success {
        background: rgba(36, 207, 166, 0.1);
        border: 1px solid var(--accent-color);
        color: var(--accent-color);
    }

    &.error {
        background: rgba(136, 39, 39, 0.1);
        border: 1px solid var(--error-color);
        color: var(--error-color);
    }

    @media (max-width: 600px) {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}

// Animation for page load
.admin-container {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Focus indicators for accessibility
.filter-dropdown:focus,
.search-input:focus,
.action-btn:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

// Custom scrollbar
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--ui-element-color);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--Lochinvar);
}
