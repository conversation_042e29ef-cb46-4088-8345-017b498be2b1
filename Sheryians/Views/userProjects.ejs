<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>My Projects - Project Dashboard</title>
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="/css/userProjects.css">
</head>

<body>
    <div class="user-container">
        <div class="user-header">
            <div class="header-content">
                <h1>Projects Dashboard</h1>
            </div>
            <div class="header-actions">
                <a href="/projects" class="submit-new-btn">
                    Submit New Project
                </a>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-content">
                    <div class="stat-number" id="totalProjects">0</div>
                    <div class="stat-label">Total Submitted</div>
                </div>
            </div>
            <div class="stat-card pending">
                <div class="stat-content">
                    <div class="stat-number" id="pendingProjects">0</div>
                    <div class="stat-label">Under Review</div>
                </div>
            </div>
            <div class="stat-card approved">
                <div class="stat-content">
                    <div class="stat-number" id="approvedProjects">0</div>
                    <div class="stat-label">Approved</div>
                </div>
            </div>
            <div class="stat-card rejected">
                <div class="stat-content">
                    <div class="stat-number" id="rejectedProjects">0</div>
                    <div class="stat-label">Rejected</div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="controls-section">
            <div class="filter-group">
                <label for="statusFilter">Filter by Status:</label>
                <select class="filter-dropdown" id="statusFilter">
                    <option value="">All Projects</option>
                    <option value="pending">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                </select>
            </div>
            <div class="search-group">
                <input type="text" class="search-input" id="searchInput" placeholder="Search your projects...">
                <div class="search-icon">🔍</div>
            </div>
        </div>

        <!-- Projects Table -->
        <div class="projects-table-container">
            <div class="projects-table">
                <div class="table-header">
                    <h3>Your Project Submissions</h3>
                    <div class="table-info">
                        <span id="projectCount">0 projects</span>
                    </div>
                </div>
                <div class="table-content">
                    <table>
                        <thead>
                            <tr>
                                <th>Project Details</th>
                                <th>Status</th>
                                <th>Submission Date</th>
                                <th>Review</th>
                            </tr>
                        </thead>
                        <tbody id="projectsTableBody">
                            <!-- Projects will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div class="loading-state" id="loadingState" style="display: none;">
            <div class="spinner"></div>
            <p>Loading your projects...</p>
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="empty-icon">📂</div>
            <h3>No Projects Yet</h3>
            <p>You haven't submitted any projects yet. Ready to share your work?</p>
            <a href="/projects" class="submit-new-btn">Submit Your First Project</a>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification">
        <span id="notificationMessage"></span>
    </div>

    <script>
        let allProjects = [];
        let filteredProjects = [];

        // Load projects on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadUserProjects();

            // Set up event listeners
            document.getElementById('statusFilter').addEventListener('change', filterProjects);
            document.getElementById('searchInput').addEventListener('input', debounce(filterProjects, 300));
        });

        async function loadUserProjects() {
            try {
                showLoading(true);
                const response = await fetch('/projects/user/my-projects');
                const data = await response.json();

                if (data.success) {
                    allProjects = data.projects;
                    updateStats();
                    filterProjects();
                } else {
                    throw new Error(data.message || 'Failed to load projects');
                }
            } catch (error) {
                console.error('Error loading projects:', error);
                showNotification('❌ Failed to load your projects', 'error');
                showEmptyState('Failed to load projects. Please try again.');
            } finally {
                showLoading(false);
            }
        }

        function updateStats() {
            const total = allProjects.length;
            const pending = allProjects.filter(p => p.status === 'pending').length;
            const approved = allProjects.filter(p => p.status === 'approved').length;
            const rejected = allProjects.filter(p => p.status === 'rejected').length;

            document.getElementById('totalProjects').textContent = total;
            document.getElementById('pendingProjects').textContent = pending;
            document.getElementById('approvedProjects').textContent = approved;
            document.getElementById('rejectedProjects').textContent = rejected;
        }

        function filterProjects() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();

            filteredProjects = allProjects.filter(project => {
                const matchesStatus = !statusFilter || project.status === statusFilter;
                const matchesSearch = !searchTerm ||
                    (project.projectTitle || '').toLowerCase().includes(searchTerm) ||
                    (project.githubLink || '').toLowerCase().includes(searchTerm);

                return matchesStatus && matchesSearch;
            });

            updateProjectCount();
            renderProjects();
        }

        function updateProjectCount() {
            const count = filteredProjects.length;
            const countText = count === 1 ? '1 project' : `${count} projects`;
            document.getElementById('projectCount').textContent = countText;
        }

        function renderProjects() {
            const tableBody = document.getElementById('projectsTableBody');

            if (filteredProjects.length === 0) {
                showEmptyState('No projects found matching your criteria');
                return;
            }

            showEmptyState('', false);

            const projectsHTML = filteredProjects.map(project => `
                <tr data-project-id="${project._id}" class="project-row ${project.status}">
                    <td>
                        <div class="project-details">
                            <h4 class="project-title">${escapeHtml(project.projectTitle || 'Untitled Project')}</h4>
                            <div class="project-links">
                                <a href="${escapeHtml(project.githubLink || '')}" target="_blank" class="project-link github">
                                    <span class="link-icon">🔗</span>
                                    GitHub Repository
                                </a>
                                ${project.liveLink ? `
                                    <a href="${escapeHtml(project.liveLink || '')}" target="_blank" class="project-link live">
                                        <span class="link-icon">🌐</span>
                                        Live Demo
                                    </a>
                                ` : ''}
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${project.status}">
                            ${formatStatus(project.status)}
                        </span>
                    </td>
                    <td>
                        <div class="date-info">
                            <div class="date-text">${formatDate(project.submittedAt)}</div>
                            <div class="time-text">${formatTime(project.submittedAt)}</div>
                        </div>
                    </td>
                    <td>
                        ${project.reviewedAt ? `
                            <div class="review-info">
                                <div class="review-date">Reviewed ${formatDate(project.reviewedAt)}</div>
                                ${project.reviewNotes ? `
                                    <div class="review-notes-preview" onclick="showReviewModal('${project._id}')">
                                        <span class="notes-icon">📝</span>
                                        View Feedback
                                    </div>
                                ` : '<div class="no-notes">No feedback provided</div>'}
                            </div>
                        ` : '<div class="pending-review">Awaiting review</div>'}
                    </td>
                    
                </tr>
            `).join('');

            tableBody.innerHTML = projectsHTML;
        }

        function formatStatus(status) {
            switch (status) {
                case 'pending': return 'Under Review';
                case 'approved': return 'Approved';
                case 'rejected': return 'Rejected';
                default: return status;
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function formatTime(dateString) {
            return new Date(dateString).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function showLoading(show) {
            const loadingState = document.getElementById('loadingState');
            const tableContainer = document.querySelector('.projects-table-container');

            if (show) {
                loadingState.style.display = 'flex';
                if (tableContainer) tableContainer.style.display = 'none';
            } else {
                loadingState.style.display = 'none';
                if (tableContainer) tableContainer.style.display = 'block';
            }
        }

        function showEmptyState(message, show = true) {
            const emptyState = document.getElementById('emptyState');
            const tableContainer = document.querySelector('.projects-table-container');

            if (show && message) {
                const messageElement = emptyState.querySelector('p');
                if (messageElement) messageElement.textContent = message;
                emptyState.style.display = 'flex';
                if (tableContainer) tableContainer.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                if (!show && tableContainer) tableContainer.style.display = 'block';
            }
        }

        function viewProject(projectId) {
            const project = allProjects.find(p => p._id === projectId);
            if (project) {
                showProjectModal(project);
            }
        }

        function showProjectModal(project) {
            // Create and show project details modal
            const modal = document.createElement('div');
            modal.className = 'project-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${escapeHtml(project.projectTitle || 'Untitled Project')}</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="project-info-grid">
                            <div class="info-item">
                                <label>GitHub Repository:</label>
                                <a href="${escapeHtml(project.githubLink || '')}" target="_blank" class="project-link">
                                    ${escapeHtml(project.githubLink || 'No repository provided')}
                                </a>
                            </div>
                            ${project.liveLink ? `
                                <div class="info-item">
                                    <label>Live Demo:</label>
                                    <a href="${escapeHtml(project.liveLink)}" target="_blank" class="project-link">
                                        ${escapeHtml(project.liveLink)}
                                    </a>
                                </div>
                            ` : ''}
                            <div class="info-item">
                                <label>Submitted:</label>
                                <span>${formatDate(project.submittedAt)} at ${formatTime(project.submittedAt)}</span>
                            </div>
                            ${project.reviewedAt ? `
                                <div class="info-item">
                                    <label>Reviewed:</label>
                                    <span>${formatDate(project.reviewedAt)} at ${formatTime(project.reviewedAt)}</span>
                                </div>
                            ` : ''}
                        </div>
                        ${project.reviewNotes ? `
                            <div class="review-feedback">
                                <h4>Review Feedback:</h4>
                                <div class="feedback-content">
                                    ${escapeHtml(project.reviewNotes)}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            setTimeout(() => modal.classList.add('show'), 10);
        }

        function closeModal() {
            const modal = document.querySelector('.project-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => modal.remove(), 300);
            }
        }

        function escapeHtml(text) {
            if (!text || typeof text !== 'string') {
                return '';
            }
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function (m) { return map[m]; });
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            const notificationMessage = document.getElementById('notificationMessage');

            notificationMessage.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('project-modal')) {
                closeModal();
            }
        });
    </script>
</body>

</html>