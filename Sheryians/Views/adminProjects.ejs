<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Project Management</title>
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="/css/adminProjects.css">
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Project Management</h1>
            <p>Review and manage all submitted projects from the community</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number" id="totalProjects">0</div>
                <div class="stat-label">Total Projects</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number" id="pendingProjects">0</div>
                <div class="stat-label">Pending Review</div>
            </div>
            <div class="stat-card approved">
                <div class="stat-number" id="approvedProjects">0</div>
                <div class="stat-label">Approved</div>
            </div>
            <div class="stat-card rejected">
                <div class="stat-number" id="rejectedProjects">0</div>
                <div class="stat-label">Rejected</div>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="controls-section">
            <select class="filter-dropdown" id="statusFilter">
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
            </select>

            <input type="text" class="search-input" id="searchInput" placeholder="Search by user name or project...">
        </div>

        <!-- Projects Table -->
        <div class="projects-table">
            <table>
                <thead>
                    <tr>
                        <th>Project</th>
                        <th>User</th>
                        <th>Status</th>
                        <th>Submitted</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="projectsTableBody">
                    <!-- Projects will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Loading State -->
        <div class="loading-state" id="loadingState" style="display: none;">
            <div class="spinner"></div>
            <p>Loading projects...</p>
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <div class="empty-icon">📂</div>
            <h3>No Projects Found</h3>
            <p>No projects match your current filters.</p>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification">
        <span id="notificationMessage"></span>
    </div>
    
    <script>
        let allProjects = [];
        let filteredProjects = [];

        // Load projects on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadProjects();

            // Set up event listeners
            document.getElementById('statusFilter').addEventListener('change', filterProjects);
            document.getElementById('searchInput').addEventListener('input', debounce(filterProjects, 300));
        });

        async function loadProjects() {
            try {
                showLoading(true);
                const response = await fetch('/projects/admin/all');
                const data = await response.json();

                if (data.success) {
                    allProjects = data.projects;
                    updateStats();
                    filterProjects();
                } else {
                    throw new Error(data.message || 'Failed to load projects');
                }
            } catch (error) {
                console.error('Error loading projects:', error);
                showNotification('❌ Failed to load projects', 'error');
                showEmptyState('Failed to load projects. Please try again.');
            } finally {
                showLoading(false);
            }
        }

        function updateStats() {
            const total = allProjects.length;
            const pending = allProjects.filter(p => p.status === 'pending').length;
            const approved = allProjects.filter(p => p.status === 'approved').length;
            const rejected = allProjects.filter(p => p.status === 'rejected').length;

            document.getElementById('totalProjects').textContent = total;
            document.getElementById('pendingProjects').textContent = pending;
            document.getElementById('approvedProjects').textContent = approved;
            document.getElementById('rejectedProjects').textContent = rejected;
        }

        function filterProjects() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();

            filteredProjects = allProjects.filter(project => {
                const matchesStatus = !statusFilter || project.status === statusFilter;
                const matchesSearch = !searchTerm ||
                    project.user.name.toLowerCase().includes(searchTerm) ||
                    project.projectTitle.toLowerCase().includes(searchTerm) ||
                    project.user.email.toLowerCase().includes(searchTerm);

                return matchesStatus && matchesSearch;
            });

            renderProjects();
        }

        function renderProjects() {
            const tableBody = document.getElementById('projectsTableBody');

            if (filteredProjects.length === 0) {
                showEmptyState('No projects found matching your criteria');
                return;
            }

            showEmptyState('', false);

            const projectsHTML = filteredProjects.map(project => `
                <tr data-project-id="${project._id}">
                    <td>
                        <div>
                            <strong>${escapeHtml(project.projectTitle)}</strong><br>
                            <a href="${escapeHtml(project.githubLink)}" target="_blank" class="project-link">
                                GitHub Repository
                            </a>
                            ${project.liveLink ? `<br><a href="${escapeHtml(project.liveLink)}" target="_blank" class="project-link">Live Demo</a>` : ''}
                        </div>
                    </td>
                    <td>
                        <div class="user-info">
                            <div class="user-name">${escapeHtml(project.user.name)}</div>
                            <div class="user-email">${escapeHtml(project.user.email)}</div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${project.status}">${project.status}</span>
                    </td>
                    <td>
                        <div class="date-text">${formatDate(project.submittedAt)}</div>
                        ${project.reviewedAt ? `<div class="date-text">Reviewed: ${formatDate(project.reviewedAt)}</div>` : ''}
                    </td>
                    <td>
                        <button class="action-btn approve" onclick="updateProjectStatus('${project._id}', 'approved')" 
                                ${project.status === 'approved' ? 'disabled' : ''}>
                            Approve
                        </button>
                        <button class="action-btn reject" onclick="updateProjectStatus('${project._id}', 'rejected')"
                                ${project.status === 'rejected' ? 'disabled' : ''}>
                            Reject
                        </button>
                    </td>
                </tr>
            `).join('');

            tableBody.innerHTML = projectsHTML;
        }

        async function updateProjectStatus(projectId, newStatus) {
            try {
                const response = await fetch(`/projects/admin/${projectId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: newStatus,
                        reviewNotes: null // Can be extended to include notes
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(`✅ Project ${newStatus} successfully`, 'success');
                    await loadProjects(); // Reload to get updated data
                } else {
                    throw new Error(data.message || 'Failed to update status');
                }
            } catch (error) {
                console.error('Error updating project status:', error);
                showNotification('❌ Failed to update project status', 'error');
            }
        }

        function showLoading(show) {
            const loadingState = document.getElementById('loadingState');
            const projectsTable = document.querySelector('.projects-table');

            if (show) {
                loadingState.style.display = 'block';
                projectsTable.style.display = 'none';
            } else {
                loadingState.style.display = 'none';
                projectsTable.style.display = 'block';
            }
        }

        function showEmptyState(message, show = true) {
            const emptyState = document.getElementById('emptyState');
            const projectsTable = document.querySelector('.projects-table');

            if (show && message) {
                emptyState.querySelector('p').textContent = message;
                emptyState.style.display = 'block';
                projectsTable.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                if (!show) projectsTable.style.display = 'block';
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function (m) { return map[m]; });
        }

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            const notificationMessage = document.getElementById('notificationMessage');

            notificationMessage.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>

</html>