<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Project Upload - Submit Your Project</title>
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="/css/projectUpload.css">
</head>

<body>
    <div id="main">
        <div class="container">
            <div class="project-form">
                <div class="form-header">
                    <h1>Submit Your Project</h1>
                    <p>Share your amazing work with the community. We'll review it and showcase the best projects!</p>
                </div>

                <form id="projectForm">
                    <div class="form-group">
                        <label for="githubLink">GitHub Repository URL <span class="required">*</span></label>
                        <input type="url" id="githubLink" name="githubLink" class="form-input"
                            placeholder="https://github.com/username/repository" required>
                        <div class="help-text">
                            Enter the URL of your GitHub repository. Make sure it's public so we can access it.
                        </div>
                        <div class="error-message" id="githubError"></div>
                    </div>

                    <div class="form-group">
                        <label for="liveLink">Live Demo URL <span class="optional">(Optional)</span></label>
                        <input type="url" id="liveLink" name="liveLink" class="form-input"
                            placeholder="https://your-project-demo.com">
                        <div class="help-text">
                            If your project is deployed, share the live demo URL here.
                        </div>
                        <div class="error-message" id="liveError"></div>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        Submit Project
                    </button>

                    <button type="button" class="viewSubmissionBtn" onclick="window.location.href='/projects/my-projects'">
                        View your submission
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification">
        <span id="notificationMessage"></span>
    </div>

    <script>
        const form = document.getElementById('projectForm');
        const submitBtn = document.getElementById('submitBtn');
        const notification = document.getElementById('notification');
        const notificationMessage = document.getElementById('notificationMessage');
        const githubInput = document.getElementById('githubLink');
        const liveInput = document.getElementById('liveLink');
        const githubError = document.getElementById('githubError');
        const liveError = document.getElementById('liveError');

        // Form submission handler
        form.addEventListener('submit', async function (e) {
            e.preventDefault();

            // Clear previous errors
            clearErrors();

            // Get form data
            const formData = {
                githubLink: githubInput.value.trim(),
                liveLink: liveInput.value.trim() || null
            };

            // Validate inputs
            if (!validateInputs(formData)) {
                return;
            }

            // Show loading state
            setLoadingState(true);

            try {
                const response = await fetch('/projects/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification('✅ Project submitted successfully! We\'ll review it soon.', 'success');
                    form.reset();
                    clearValidationStates();
                } else {
                    throw new Error(result.message || 'Submission failed');
                }
            } catch (error) {
                console.error('Error submitting project:', error);
                showNotification('❌ ' + (error.message || 'Submission failed. Please try again.'), 'error');
            } finally {
                setLoadingState(false);
            }
        });

        function validateInputs(formData) {
            let isValid = true;

            // Validate GitHub link
            if (!formData.githubLink) {
                showFieldError('githubError', 'GitHub repository URL is required');
                githubInput.classList.add('error');
                isValid = false;
            } else if (!formData.githubLink.includes('github.com')) {
                showFieldError('githubError', 'Please enter a valid GitHub repository URL');
                githubInput.classList.add('error');
                isValid = false;
            } else {
                githubInput.classList.add('success');
            }

            // Validate live link if provided
            if (formData.liveLink && !isValidUrl(formData.liveLink)) {
                showFieldError('liveError', 'Please enter a valid URL');
                liveInput.classList.add('error');
                isValid = false;
            } else if (formData.liveLink) {
                liveInput.classList.add('success');
            }

            return isValid;
        }

        function showFieldError(errorId, message) {
            const errorElement = document.getElementById(errorId);
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        function clearErrors() {
            githubError.classList.remove('show');
            liveError.classList.remove('show');
            clearValidationStates();
        }

        function clearValidationStates() {
            githubInput.classList.remove('error', 'success');
            liveInput.classList.remove('error', 'success');
        }

        function setLoadingState(loading) {
            submitBtn.disabled = loading;
            if (loading) {
                submitBtn.classList.add('loading');
                submitBtn.textContent = 'Submitting...';
            } else {
                submitBtn.classList.remove('loading');
                submitBtn.textContent = 'Submit Project';
            }
        }

        function showNotification(message, type) {
            notificationMessage.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }

        function isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (_) {
                return false;
            }
        }

        // Real-time validation
        githubInput.addEventListener('input', function () {
            const value = this.value.trim();
            if (value && value.includes('github.com')) {
                this.classList.remove('error');
                this.classList.add('success');
                githubError.classList.remove('show');
            } else if (value) {
                this.classList.remove('success');
                this.classList.add('error');
                showFieldError('githubError', 'Please enter a valid GitHub repository URL');
            } else {
                clearValidationStates();
                githubError.classList.remove('show');
            }
        });

        liveInput.addEventListener('input', function () {
            const value = this.value.trim();
            if (value && isValidUrl(value)) {
                this.classList.remove('error');
                this.classList.add('success');
                liveError.classList.remove('show');
            } else if (value) {
                this.classList.remove('success');
                this.classList.add('error');
                showFieldError('liveError', 'Please enter a valid URL');
            } else {
                clearValidationStates();
                liveError.classList.remove('show');
            }
        });
    </script>
</body>

</html>